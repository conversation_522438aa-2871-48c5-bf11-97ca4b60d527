import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'

const cartPageSection: { label: string, value: string, db: string }[] = [
    {
        label: "Cart Items",
        value: "CartItems",
        db: "cartItems",
    },
    {
        label: "Order Summary",
        value: "OrderSummary",
        db: "orderSummary",
    },
    {
        label: "Subtotal",
        value: "Subtotal",
        db: "subtotal",
    },
    {
        label: "Items",
        value: "Items",
        db: "items",
    },
    {
        label: "Loyalty Discount",
        value: "LoyaltyDiscount",
        db: "loyaltyDiscount",
    },
    {
        label: "Savings",
        value: "Savings",
        db: "savings",
    },
    {
        label: "Try Cart Deduction",
        value: "TryCartDeduction",
        db: "tryCartDeduction",
    },
    {
        label: "Shipping",
        value: "Shipping",
        db: "shipping",
    },
    {
        label: "Total",
        value: "Total",
        db: "total",
    },
    {
        label: "View Offers",
        value: "ViewOffers",
        db: "viewOffers",
    },
    {
        label: "Offers Title",
        value: "OffersTitle",
        db: "offersTitle",
    },
    {
        label: "Coupon Offers",
        value: "CouponOffers",
        db: "couponOffers",
    },
    {
        label: "No Offers",
        value: "NoOffers",
        db: "noOffers",
    },
    {
        label: "Discount Code",
        value: "DiscountCode",
        db: "discountCode",
    },
    {
        label: "Discount Code Text",
        value: "DiscountCodeText",
        db: "discountCodeText",
    },
    {
        label: "Coupon Code",
        value: "CouponCode",
        db: "couponCode",
    },
    {
        label: "Apply Coupon",
        value: "ApplyCoupon",
        db: "applyCoupon",
    },
    {
        label: "Remove",
        value: "Remove",
        db: "remove",
    },
    {
        label: "Loyalty",
        value: "Loyalty",
        db: "loyalty",
    },
    {
        label: "Redeemable Amount",
        value: "RedeemableAmount",
        db: "redeemableAmount",
    },
    {
        label: "Redeemed",
        value: "Redeemed",
        db: "redeemed",
    },
    {
        label: "Redeem",
        value: "Redeem",
        db: "redeem",
    },
    {
        label: "Checkout",
        value: "Checkout",
        db: "checkout",
    },
    {
        label: "Add Shipping",
        value: "AddShipping",
        db: "addShipping",
    },
    {
        label: "Continue To Delivery",
        value: "ContinueToDelivery",
        db: "continueToDelivery",
    },
    {
        label: "Your Cart Is Empty",
        value: "YourCartIsEmpty",
        db: "yourCartIsEmpty"
    },
    {
        label: "Cart Empty Msg",
        value: "CartEmptyMsg",
        db: "cartEmptyMsg"
    },
    {
        label: "Go Shop",
        value: "GoShop",
        db: "goShop"
    },
    {
        label: "Sphere Left",
        value: "SphereLeft",
        db: "sphereLeft",
    },
    {
        label: "Sphere Right",
        value: "SphereRight",
        db: "sphereRight",
    },
    {
        label: "Cylinder Left",
        value: "CylinderLeft",
        db: "cylinderLeft",
    },
    {
        label: "Cylinder Right",
        value: "CylinderRight",
        db: "cylinderRight",
    },
    {
        label: "Axis Right",
        value: "AxisRight",
        db: "axisRight",
    },
    {
        label: "Axis left",
        value: "Axisleft",
        db: "axisleft",
    },
    {
        label: "Tamara",
        value: "Tamara",
        db: "tamara",
    },
    {
        label: "Tamara Description",
        value: "TamaraDescription",
        db: "tamaraDesc",
    },
    {
        label: "Tabby",
        value: "Tabby",
        db: "tabby",
    },
    {
        label: "Tabby Description",
        value: "TabbyDescription",
        db: "tabbyDesc",
    },
    {
        label: "Gift Wrapping",
        value: "GiftWrapping",
        db: "giftWrapping",
    },
]

const indicators: { label: string, value: string, db: string }[] = [
    {
        label: "Shipping Details",
        value: "ShippingDetails",
        db: "shippingDetails",
    },
    {
        label: "Shipping Methods",
        value: "ShippingMethods",
        db: "shippingMethods",
    },
    {
        label: "Payment Methods",
        value: "PaymentMethods",
        db: "paymentMethods",
    },
]

const delivery: { label: string, value: string, db: string }[] = [
    {
        label: "Doorstep Delivery",
        value: "DoorstepDelivery",
        db: "doorstepDelivery",
    },
    {
        label: "Doorstep Delivery Text",
        value: "DoortsepDeliveryText",
        db: "doorstepDeliveryText",
    },
    {
        label: "Continue To Payment",
        value: "ContinueToPayment",
        db: "continueToPayment",
    },
]

const payment: { label: string, value: string, db: string }[] = [
    {
        label: "Card",
        value: "Card",
        db: "card",
    },
    {
        label: "Cod",
        value: "Cod",
        db: "cod",
    },
    {
        label: "Cod Text",
        value: "CodText",
        db: "codText",
    },
    {
        label: "Place Order",
        value: "PlaceOrder",
        db: "placeOrder",
    },
]


export const cartPage: { label: string, value: string, db: string }[] = [
    ...cartPageSection,
    ...indicators,
    ...delivery,
    ...payment,
]


function CartTab({ control, errors }: any) {
    return (
        <>
            <h3>Cart Page</h3>
            {cartPageSection.map((item, index) => (
                <Forms key={index + item.value} item={item} control={control} errors={errors} />
            ))}
            <h3>Indicators</h3>
            {indicators.map((item, index) => (
                <Forms key={index + item.value} item={item} control={control} errors={errors} />
            ))}
            <h3>Delivery Methods</h3>
            {delivery.map((item, index) => (
                <Forms key={index + item.value} item={item} control={control} errors={errors} />
            ))}
            <h3>Payment Methods</h3>
            {payment.map((item, index) => (
                <Forms key={index + item.value} item={item} control={control} errors={errors} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`cartPage${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`cartPage${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`cartPage${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`cartPage${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`cartPage${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`cartPage${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}
export default CartTab