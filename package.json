{"name": "yateem-admin", "private": true, "version": "2.0.1", "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "npm run lint -- --fix", "prettier": "npx prettier src --check", "prettier:fix": "npm run prettier -- --write", "format": "npm run prettier:fix && npm run lint:fix"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@reduxjs/toolkit": "^1.9.3", "@tanstack/match-sorter-utils": "^8.8.4", "@tanstack/react-table": "^8.8.5", "@visx/pattern": "^3.0.0", "apexcharts": "^3.37.3", "axios": "^1.3.4", "classnames": "^2.3.2", "d3-dsv": "^3.0.1", "d3-fetch": "^3.0.1", "d3-scale": "^4.0.2", "dayjs": "^1.11.7", "export-to-csv": "^1.2.4", "formik": "^2.2.9", "framer-motion": "^10.8.5", "history": "^5.3.0", "html-react-parser": "^3.0.15", "i18next": "^22.4.13", "lodash": "^4.17.21", "miragejs": "^0.1.47", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-beautiful-dnd": "^13.1.1", "react-custom-scrollbars-2": "^4.5.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.20.0", "react-hook-form": "^7.48.2", "react-i18next": "^12.2.0", "react-icons": "^5.5.0", "react-markdown": "^8.0.7", "react-modal": "^3.16.1", "react-number-format": "^5.1.4", "react-popper": "^2.3.0", "react-portal": "^4.2.2", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.9.0", "react-scroll": "^1.8.9", "react-select": "^5.7.2", "react-simple-maps": "^3.0.0", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "^5.11.1", "redux-persist": "^6.0.0", "twin.macro": "^3.3.0", "yup": "^1.1.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.9", "@types/d3-fetch": "^3.0.2", "@types/d3-scale": "^4.0.3", "@types/lodash": "^4.14.191", "@types/node": "^18.15.5", "@types/react": "^18.0.28", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-dom": "^18.0.11", "@types/react-highlight-words": "^0.16.4", "@types/react-modal": "^3.13.1", "@types/react-portal": "^4.0.4", "@types/react-scroll": "^1.8.7", "@types/react-simple-maps": "^3.0.0", "@types/react-syntax-highlighter": "^15.5.6", "@typescript-eslint/eslint-plugin": "^5.56.0", "@typescript-eslint/parser": "^5.56.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "cross-env": "^7.0.3", "dotenv": "^16.0.3", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "^8.4.21", "postcss-cli": "^10.1.0", "postcss-nesting": "^11.2.1", "prettier": "^2.8.6", "rollup-plugin-polyfill-node": "^0.12.0", "rollup-plugin-postcss": "^4.0.2", "tailwindcss": "^3.3.1", "typescript": "^4.9.3", "vite": "^4.3.3", "vite-plugin-dynamic-import": "^1.3.4"}}