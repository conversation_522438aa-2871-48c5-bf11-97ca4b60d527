import Card from '@/components/ui/Card'
import { NumericFormat } from 'react-number-format'
import GrowShrinkTag from '@/components/shared/GrowShrinkTag'
import dayjs from 'dayjs'
import { Link } from 'react-router-dom'

type StatisticCardProps = {
    data?: {
        value: number
        growShrink: number
    }
    label: string
    valuePrefix?: string
    date: number
    linkTo: string
}

type StatisticProps = {
    data?: {
        revenue?: {
            value: number
            growShrink: number
        }
        orders?: {
            value: number
            growShrink: number
        }
        purchases?: {
            value: number
            growShrink: number
        }
    }
}

const getThreeMonthPriorDate = () => {
    const today = new Date()
    const threeMonthsAgo = new Date(today.setMonth(today.getMonth()))
    return threeMonthsAgo.getTime()
}

const StatisticCard = ({
    data = { value: 0, growShrink: 0 },
    label,
    valuePrefix,
    date,
    linkTo,
}: StatisticCardProps) => {
    return (
        <Card>
            <Link to={linkTo}>
            <h6 className="font-semibold mb-4 text-sm">{label}</h6>
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="font-bold">
                        <NumericFormat
                            thousandSeparator
                            displayType="text"
                            value={data.value}
                            prefix={valuePrefix}
                        />
                    </h3>
                    <p>
                        vs. 3 months prior to{' '}
                        <span className="font-semibold">
                            {dayjs(date).format('DD MMM')}
                        </span>
                    </p>
                </div>
                <GrowShrinkTag value={data.growShrink} suffix="%" />
            </div>
            </Link>
        </Card>
    )
}

const Statistic = ({ data = {} }: StatisticProps) => {
    const startDate = getThreeMonthPriorDate()

    return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <StatisticCard
                data={data.revenue}
                valuePrefix="AED"
                label="Revenue"
                date={startDate}
                linkTo='/revenue-report'
            />
            <StatisticCard  data={data.orders} label="Orders" date={startDate} linkTo='/orders' />
            <StatisticCard
                data={data.purchases}
                valuePrefix="AED"
                label="Average Sale Amount"
                date={startDate}
                linkTo='/sales-report'
            />
        </div>
    )
}

export default Statistic
