import { LaptopMockup, PhoneMockup } from '@/components/shared/Mockup'
import { FormItem, Input, Upload } from '@/components/ui'
import React from 'react'

const imageUrl = import.meta.env.VITE_ASSET_URL

function ManageOfferBanner({ pageSection, handleSliderVideo }: any) {
    return (
        <>
            <h5>Offer Banner Section</h5>
            <div className="mt-4 grid grid-cols-2 gap-4">
                <FormItem label="Banner Image English One">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={pageSection?.banner?.en ? [imageUrl + pageSection?.banner?.en] : []}
                        onChange={(e) => handleSliderVideo({ target: { files: e } }, `banner.en`, pageSection?.id)}
                        ratio={[1332, 240]}
                    />
                </FormItem>
                <FormItem label="Banner Image English Two">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={pageSection?.bannerTwo?.en ? [imageUrl + pageSection?.bannerTwo?.en] : []}
                        onChange={(e) => handleSliderVideo({ target: { files: e } }, `bannerTwo.en`, pageSection?.id)}
                        ratio={[1332, 240]}
                    />
                </FormItem>
            </div>
            <div className="mt-4 grid grid-cols-2 gap-4">
                <FormItem label="Banner Image Arabic One">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={pageSection?.banner?.ar ? [imageUrl + pageSection?.banner?.ar] : []}
                        onChange={(e) => handleSliderVideo({ target: { files: e } }, `banner.ar`, pageSection?.id)}
                        ratio={[1332, 240]}
                    />
                </FormItem>
                <FormItem label="Banner Image Arabic Two">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={pageSection?.bannerTwo?.ar ? [imageUrl + pageSection?.bannerTwo?.ar] : []}
                        onChange={(e) => handleSliderVideo({ target: { files: e } }, `bannerTwo.ar`, pageSection?.id)}
                        ratio={[1332, 240]}
                    />
                </FormItem>
            </div>
        </>
    )
}

export default ManageOfferBanner