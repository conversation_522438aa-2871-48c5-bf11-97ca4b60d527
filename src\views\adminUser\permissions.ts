
export const permissions = [
    'Dashboard',
    'Home Page Customization',
    'Brands' ,
    'Categories',
    'Products',
    'Contact Lens',
    'Collections',
    'Non List Items',
    'Colors',
    'Sizes',
    'Frame Types',
    'Frame Shapes',
    'Age Groups',
    'Front Materials',
    'Types',
    'Lens Materials',
    'Customers',
    'Cart',
    'Orders',
    'Try Cart Orders',
    'Blogs',
    'Newsletters',
    'Insurance Enquiries',
    'Contact Us Enquiries',
    'Product Enquiries',
    'Lens Enquiries',
    'Lens Brands',
    'Lens Power',
    'Lens Type',
    'Lens Index',
    'Lens Coating',
    'Stores',
    'Admin Users',
    'Roles',
    'Insurance Providers',
    'Contact us page',
    'Insurance page',
    'Contact Lens Page',
    'FAQ',
    'Terms and Conditions',
    'Privacy Policy',
    'Refund Policy',
    'Shipping Policy',
    'Return Policy',
    'Cookie Policy',
    'About Us',
    'Subscription Plans',
    'Subscribers',
    'Header Menu',
    'Footer Menu',
    'General Settings',
    'Try cart settings',
    'Reports',
    'Reviews',
    'Meta Tags',
    'Additional Fees',
    'VM Policy',
    'Translation',
    'Contact Lens Sizes'
]


export const permissionObject:any = {
    "dashboard-rearrange": 'Home Page Customization',
    "image-map": 'Home Page Customization',
    "brands":'Brands' ,
    "manage-brand": 'Brands' ,
    "categories": 'Categories',
    "manage-categories": 'Categories',
    "manage-sub-categories": 'Categories',
    "products": 'Products',
    "manage-products": 'Products',
    "variants" : 'Products',
    "collections": 'Collections',
    "manage-collections": 'Collections',
    "colors": 'Colors',
    "manage-colors": 'Colors',
    "sizes": 'Sizes',
    "manage-sizes": 'Sizes',
    "frame-types": 'Frame Types',
    "manage-frame-types": 'Frame Types',
    "frame-shapes": 'Frame Shapes',
    "manage-frame-shapes": 'Frame Shapes',
    "age-group": 'Age Groups',
    "manage-age-group": 'Age Groups',
    "label": 'Labels',
    "manage-label": 'Labels',
    "front-material": 'Front Materials',
    "manage-front-material": 'Front Materials',
    "types": 'Types',
    "manage-types": 'Types',
    "lens-material": 'Lens Materials',
    "manage-lens-material": 'Lens Materials',
    "customers": 'Customers',
    "manage-customers": 'Customers',
    "cart": 'Cart',
    "orders": 'Orders',
    "generate-invoice" : 'Orders',
    "try-cart-order" : 'Try Cart Orders',
    "manage-orders": 'Orders',
    "blogs": 'Blogs',
    "manage-blogs": 'Blogs',
    "news-letter": 'Newsletters',
    "insurance-enquiries": 'Insurance Enquiries',
    "contact-us-enquiries": 'Contact Us Enquiries',
    "lens-brand": 'Lens Brands',
    "manage-lens-brand": 'Lens Brands',
    "lens-power": 'Lens Power',
    "manage-lens-power": 'Lens Power',
    "Sph": 'Lens Power',
    "Cyl": 'Lens Power',
    "Axis": 'Lens Power',
    "Add": 'Lens Power',
    "lens-type": 'Lens Type',
    "manage-lens-type": 'Lens Type',
    "lens-index": 'Lens Index',
    "manage-lens-index": 'Lens Index',
    "coating": 'Lens Coating',
    "manage-coating": 'Lens Coating',
    "stores": 'Stores',
    "manage-store": 'Stores',
    "admins" : 'Admin Users',
    "manage-admins": 'Admin Users',
    "roles": 'Roles',
    "manage-roles": 'Roles',
    "insurance-provider": 'Insurance Providers',
    "manage-insurance-provider": 'Insurance Providers',
    "contact-banner": 'Contact us page',
    "insurance-contents": 'Insurance page',
    "contact-lens" : 'Contact Lens Page',
    "faq":"FAQ",
    "terms" : 'Terms and Conditions',
    "privacy-policy":'Privacy Policy',
    "cookie-policy" : 'Cookie Policy',
    "refund-policy" : 'Refund Policy',
    "shipping-policy" : 'Shipping Policy',
    "return-policy" : 'Return Policy',
    "about-us" : 'About Us',
    "subscription-plans" : 'Subscription Plans',
    "manage-subscription-plans": 'Subscription Plans',
    "subscriptions" : 'Subscribers',
    "header-menu" : 'Header Menu',
    "manage-header-menu": 'Header Menu',
    "footer" : 'Footer Menu',
    "general-settings" : 'General Settings',
    "try-cart" : 'Try cart settings',
    "sales-report" : 'Reports',
    "revenue-report": 'Reports',
    "top-selling-report": 'Reports',
    "customer-report": 'Reports',
    "guest-report": 'Reports',
    "non-list" : 'Non List Items',
    "manage-non-list" : 'Non List Items',
    "product-enquiries" : 'Product Enquiries',
    "lens-enquiries" : 'Lens Enquiries',
    "reviews" : 'Reviews',
    "meta-tags" : 'Meta Tags',
    "contact-lenses" : 'Contact Lens',
    "manage-contact-lenses" : 'Contact Lens',
    "contact-lens-power" : 'Contact Lens',
    "additional-fees" : 'Additional Fees',
    "vm-policy": 'VM Policy',
    "translation": "Translation",
    "contact-lens-sizes": "Contact Lens Sizes",
    "manage-contact-lens-sizes": "Contact Lens Sizes",
}