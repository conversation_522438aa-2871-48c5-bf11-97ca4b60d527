/* eslint-disable */
import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import { useReactTable, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, flexRender, getSortedRowModel, } from '@tanstack/react-table'
import type { ColumnDef, ColumnFiltersState, FilterFn, } from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import Button from '@/components/ui/Button'
import { AiOutlinePlus } from 'react-icons/ai'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { Link } from 'react-router-dom'
import Breadcrumb from '@/views/modals/BreadCrumb'
import { DatePicker, Dialog, FormItem, Tag, toast } from '@/components/ui'
import Notification from '@/components/ui/Notification'
import { MdDeleteOutline } from 'react-icons/md'
import DeleteModal from '../modals/DeleteModal'

interface DebouncedInputProps
    extends Omit<
        InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'size' | 'prefix'
    > {
    value: string | number
    onChange: (value: string | number) => void
    debounce?: number
}

const { Tr, Th, Td, THead, TBody, Sorter } = Table

function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: DebouncedInputProps) {
    const [value, setValue] = useState(initialValue)

    useEffect(() => {
        setValue(initialValue)
    }, [initialValue])

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)
        }, debounce)

        return () => clearTimeout(timeout)
    }, [value])

    return (
        <div className="flex justify-end">
            <div className="flex items-center mb-4">
                <Input
                    {...props}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                />
            </div>
        </div>
    )
}

type Option = {
    value: number
    label: string
}

const pageSizeOption = [
    { value: 10, label: '10 / page' },
    { value: 20, label: '20 / page' },
    { value: 30, label: '30 / page' },
    { value: 40, label: '40 / page' },
    { value: 50, label: '50 / page' },
]

const breadcrumbItems = [
    { title: 'News Letter', url: '' },
];

const NewsLetter = () => {
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [globalFilter, setGlobalFilter] = useState('')
    const [open, setOpen] = useState(false)
    const [email, setEmail] = useState('')
    const [error, setError] = useState('')
    const [submit, setSubmit] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [unSubscribeModalOpen, setUnSubscribeModalOpen] = useState(false)
    const [subscribeModalOpen, setSubscribeModalOpen] = useState(false)
    const [itemToDelete, setItemToDelete] = useState(null)
    const [itemToUnSubscribe, setItemToUnSubscribe] = useState(null)
    const [itemToSubscribe, setItemToSubscribe] = useState(null)

    const [fromDate, setFromDate] = useState('')
    const [toDate, setToDate] = useState('')
    const [status, setStatus] = useState('')

    const openDeleteModal = (refid: any) => {
        setDeleteModalOpen(true)
        setItemToDelete(refid)
    }

    const openUnSubscribeModal = (refid: any) => {
        setUnSubscribeModalOpen(true)
        setItemToUnSubscribe(refid)
    }

    const openSubscribeModal = (refid: any) => {
        setSubscribeModalOpen(true)
        setItemToSubscribe(refid)
    }

    const closeDeleteModal = () => {
        setDeleteModalOpen(false)
    }

    const closeUnSubscribeModal = () => {
        setUnSubscribeModalOpen(false)
    }

    const closeSubscribeModal = () => {
        setSubscribeModalOpen(false)
    }

    const confirmUnSubscribe = () => {
        api.put(endpoints.unSubscribeNewsletter + itemToUnSubscribe)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    closeUnSubscribeModal()
                    setSubmit((prev: boolean) => !prev)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                closeDeleteModal()
            })
    }

    const confirmDelete = () => {
        api.delete(endpoints.deleteNewsletter + itemToDelete)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    closeDeleteModal()
                    setSubmit((prev: boolean) => !prev)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                closeDeleteModal()
            })
    }

    const confirmSubscribe = () => {
        const ExistEmail = data?.find((item: any) => item.refid === itemToSubscribe)?.email;
        if (ExistEmail) {
            api.post(endpoints.addNewsletter, { email: ExistEmail }).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    // setOpen(false)
                    setSubscribeModalOpen(false)
                    setSubmit((prev: boolean) => !prev)
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        }
    }

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                header: 'Sl No.',
                accessorKey: 'slNo',
                cell: (info) => info.row.index + 1,
                enableSorting: false,
            },
            {
                header: 'Email',
                accessorKey: 'email',
                enableSorting: false,
            },
            {
                header: 'Status',
                accessorKey: 'isActive',
                enableSorting: false,
                cell: (info) => {
                    const isActive = info.getValue();
                    return (
                        <button onClick={info.getValue() == true ? () => openUnSubscribeModal(info.row.original.refid) : () => openSubscribeModal(info.row.original.refid)}>
                            <Tag className={isActive ? 'bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded' : 'text-red-600 bg-red-100 dark:text-red-100 dark:bg-red-500/20 border-0 rounded'} >
                                {isActive ? 'Subscribed' : 'Unsubscribed'}
                            </Tag>
                        </button>
                    )
                },
            },
            {
                header: 'Date',
                accessorKey: 'createdAt',
                enableSorting: false,
                cell: (info: any) => {
                    const date = new Date(info.getValue())
                    const formattedDate = date.toLocaleDateString("en-GB")
                    return <div>{formattedDate}</div>
                },
            },
            {
                header: 'Actions',
                accessorKey: 'refid',
                enableSorting: false,
                cell: (info) => {
                    return (
                        <div className="flex items-center">
                            <div
                                onClick={() => openDeleteModal(info.getValue())}
                            >
                                <MdDeleteOutline size={25} className="ml-4" />
                            </div>
                        </div>
                    )
                },
            }
        ],
        []
    )

    const [data, setData] = useState<any[]>([])
    const totalData = data?.length


    const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
        const itemRank = rankItem(row.getValue(columnId), value)

        addMeta({
            itemRank,
        })

        return itemRank.passed
    }

    const table = useReactTable({
        data,
        columns,
        filterFns: {
            fuzzy: fuzzyFilter,
        },
        state: {
            columnFilters,
            globalFilter,
        },
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        globalFilterFn: fuzzyFilter,
        getSortedRowModel: getSortedRowModel(),
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
    })

    const onPaginationChange = (page: number) => {
        table.setPageIndex(page - 1)
    }

    const onSelectChange = (value = 0) => {
        table.setPageSize(Number(value))
    }

    const validate = () => {
        if (!email) {
            setError('Email is required')
            return false
        }
        const regex = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;
        if (!regex.test(email)) {
            setError('Invalid email address')
            return false
        }
        return true
    }


    const onSubmit = () => {
        const valid = validate()
        if (valid) {
            api.post(endpoints.addNewsletter, { email: email }).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    setOpen(false)
                    setSubmit(true)
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        }
    }

    useEffect(() => {
        const payload = {
            fromDate: fromDate,
            toDate: toDate,
        }
        api
            .post(endpoints.newsletter, payload)
            .then((res) => {
                setData(res?.data?.result)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }, [submit, fromDate, toDate])

    const activeFilter = (option: any) => {
        let filters = columnFilters
        const index = filters.findIndex((item) => item.id == "isActive")
        console.log(index)
        if (option.value) {
            if (index != -1) filters[index].value = option.value == "true" ? true : option.value == "false" ? false : ""
            else filters.push({ id: 'isActive', value: option.value == "true" ? true : option.value == "false" ? false : "" })
        } else {
            if (index != -1) filters.splice(index, 1)
        }
        console.log(filters)
        setColumnFilters([...filters])
    }

    return (
        <div>
            <div className='mb-4 flex'>
                <h2>News Letter</h2>
                <div className='mr-2 mb-2 ml-auto'>
                    {/* <Link to='/'>
                        <Button variant="twoTone" color="green-600" icon={<AiOutlinePlus />}>
                            Create Newsletter
                        </Button>
                    </Link> */}
                    <Button className='ml-2' onClick={() => setOpen(true)} variant='twoTone' color='purple-600' icon={<AiOutlinePlus />}>
                        Add New Subscriber
                    </Button>
                </div>
            </div>
            <Breadcrumb items={breadcrumbItems} />

            <div className="flex gap-3 justify-end mb-4">
                <div className="w-[200-px]">
                    <DatePicker
                        placeholder="From Date"
                        onChange={(date: any) => {
                            console.log('date', date)
                            setFromDate((prev) => {
                                return date
                            })
                        }}
                    />
                </div>
                <div className="w-[200-px]">
                    <DatePicker
                        placeholder="To Date"
                        onChange={(date: any) => {
                            setToDate((prev) => {
                                return date
                            })
                        }}
                    />
                </div>
            </div>
            <div className="mb-4 flex space-x-2 justify-end">
                <FormItem className='w-[200px]' label="Status">
                    <Select
                        placeholder="Select a Status"
                        options={[
                            { value: "", label: 'All' },
                            { value: "true", label: 'Subscribed' },
                            { value: "false", label: 'Unsubscribed' },
                        ]}
                        defaultValue={{ value: "", label: 'All' }}
                        onChange={activeFilter}
                    />
                </FormItem>
                <DebouncedInput
                    value={globalFilter ?? ''}
                    className="p-2 font-lg shadow border border-block"
                    placeholder="Search all columns..."
                    onChange={(value) => setGlobalFilter(String(value))}
                />
            </div>
            <Table>
                <THead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <Tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <Th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                    >
                                        {header.column.getCanSort() ? (
                                            <div
                                                className="cursor-pointer select-none"
                                                onClick={header.column.getToggleSortingHandler()}
                                            >
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                                <Sorter sort={header.column.getIsSorted()} />
                                            </div>
                                        ) : (
                                            <div>
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                            </div>
                                        )}
                                    </Th>
                                )
                            })}
                        </Tr>
                    ))}
                </THead>
                <TBody>
                    {table.getRowModel().rows.map((row) => {
                        return (
                            <Tr key={row.id}>
                                {row.getVisibleCells().map((cell) => {
                                    return (
                                        <Td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Td>
                                    )
                                })}
                            </Tr>
                        )
                    })}
                </TBody>
            </Table>
            <div className="flex items-center justify-between mt-4">
                <Pagination
                    pageSize={table.getState().pagination.pageSize}
                    currentPage={table.getState().pagination.pageIndex + 1}
                    total={totalData}
                    onChange={onPaginationChange}
                />
                <div style={{ minWidth: 130 }}>
                    <Select<Option>
                        size="sm"
                        isSearchable={false}
                        value={pageSizeOption.filter(
                            (option) =>
                                option.value ===
                                table.getState().pagination.pageSize
                        )}
                        options={pageSizeOption}
                        onChange={(option) => onSelectChange(option?.value)}
                    />
                </div>
            </div>

            <Dialog
                isOpen={open}
                onClose={() => setOpen(false)}
            >
                <h5 className='my-4'>Subscribe To Newsletter</h5>
                <FormItem label='Email'>
                    <Input
                        type='email'
                        placeholder='Enter Email'
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                    />
                    {error && <small className='text-red-600'>{error}</small>}
                </FormItem>
                <div className='flex justify-end'>
                    <Button onClick={onSubmit} className='' variant='solid' color='green-600'>Subscribe </Button>
                </div>
            </Dialog>
            <DeleteModal
                isOpen={deleteModalOpen}
                title="Delete Subscriber"
                content={`Are you sure you want to delete the subscriber ${data?.find((item: any) => item.refid === itemToDelete)?.email}?`}
                onClose={closeDeleteModal}
                onConfirm={confirmDelete}
            />
            <DeleteModal
                isOpen={unSubscribeModalOpen}
                title="Unsubscribe"
                content={`Are you sure you want to Unsubscribe ${data?.find((item: any) => item.refid === itemToUnSubscribe)?.email}?`}
                onClose={closeUnSubscribeModal}
                onConfirm={confirmUnSubscribe}
            />
            <DeleteModal
                isOpen={subscribeModalOpen}
                title="Re-subscribe"
                content={`Are you sure you want to Re-subscribe ${data?.find((item: any) => item.refid === itemToSubscribe)?.email}?`}
                onClose={closeSubscribeModal}
                onConfirm={confirmSubscribe}
            />
        </div>
    )
}

export default NewsLetter