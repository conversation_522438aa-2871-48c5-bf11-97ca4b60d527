import { useMemo, lazy, Suspense, useEffect } from 'react'
import Loading from '@/components/shared/Loading'
import { useAppSelector } from '@/store'
import {
    LAYOUT_TYPE_CLASSIC,
    LAYOUT_TYPE_MODERN,
    LAYOUT_TYPE_SIMPLE,
    LAYOUT_TYPE_STACKED_SIDE,
    LAYOUT_TYPE_DECKED,
    LAYOUT_TYPE_BLANK,
} from '@/constants/theme.constant'
import useAuth from '@/utils/hooks/useAuth'
import useDirection from '@/utils/hooks/useDirection'
import useLocale from '@/utils/hooks/useLocale'
import { useLocation, useNavigate } from 'react-router-dom'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { permissionObject } from '@/views/adminUser/permissions'

const layouts = {
    [LAYOUT_TYPE_CLASSIC]: lazy(() => import('./ClassicLayout')),
    [LAYOUT_TYPE_MODERN]: lazy(() => import('./ModernLayout')),
    [LAYOUT_TYPE_STACKED_SIDE]: lazy(() => import('./StackedSideLayout')),
    [LAYOUT_TYPE_SIMPLE]: lazy(() => import('./SimpleLayout')),
    [LAYOUT_TYPE_DECKED]: lazy(() => import('./DeckedLayout')),
    [LAYOUT_TYPE_BLANK]: lazy(() => import('./BlankLayout')),
}

const Layout = () => {
    const layoutType = useAppSelector((state) => state.theme.layout.type)

    console.log(layoutType)

    const { authenticated } = useAuth()

    useDirection()

    useLocale()

    const location = useLocation()
    const navigate = useNavigate()

    useEffect(() => {
        console.log(location.pathname, 'location')
        const pathParts = location.pathname?.split('/')
        const secondPart = pathParts?.[2]
        const firstPart = pathParts?.[1]
        console.log(Number(secondPart), 'secondPart')
        const permissionKey =
           ( secondPart && !isNaN(Number(secondPart)) ) || firstPart==="manage-orders" || firstPart=== "generate-invoice" || firstPart=== "image-map"
                ? pathParts?.[1] || 'home'
                : secondPart || pathParts?.[1] || 'home'
        if (
            location.pathname === '/access-denied' ||
            location.pathname === '/home' ||
            location.pathname === '/sign-in' ||
            location.pathname === '/forgot-password' ||
            location.pathname === '/reset-password' ||
            location.pathname === '/' ||
            location.pathname === '/login'
        )
            return
           console.log(permissionObject[permissionKey], 'permissionObject[permissionKey]', permissionKey) 
        api.post(endpoints.checkPermission, {
            permission: permissionObject[permissionKey],
        })
            .then((res) => {
                console.log(res, 'res')
                if (res.status == 200) {
                }
            })
            .catch((err) => {
                console.log(err, 'err')
                navigate('/access-denied')
            })
    }, [location])

    const AppLayout = useMemo(() => {
        if (authenticated) {
            return layouts[layoutType]
        }
        return lazy(() => import('./AuthLayout'))
    }, [layoutType, authenticated])

    return (
        <Suspense
            fallback={
                <div className="flex flex-auto flex-col h-[100vh]">
                    <Loading loading={true} />
                </div>
            }
        >
            <AppLayout />
        </Suspense>
    )
}

export default Layout
