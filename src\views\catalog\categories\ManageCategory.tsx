import { Controller, useForm } from 'react-hook-form'
import Button from '@/components/ui/Button'
import { AiOutlineSave } from 'react-icons/ai'
import { Dialog, FormItem, Input, Select, Upload } from '@/components/ui'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { useEffect, useState } from 'react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Breadcrumb from '@/views/modals/BreadCrumb'
import ManageSubCategory from './ManageSubCategory'
import { MdAddCircleOutline, MdDeleteOutline } from 'react-icons/md'
import { HiPencilSquare } from 'react-icons/hi2'
import DeleteModal from '@/views/modals/DeleteModal'

/* eslint-disable */
interface Inputs {
    nameEn: any
    nameAr: any
    isRoot: any
    parentCategory: any
    isActive: any
    topCategory: any
    isCashbackEnabled: any
    cashbackPercentage: any
    metaTitleEn: any
    metaTitleAr: any
    metaDescriptionEn: any
    metaDescriptionAr: any
    metaKeywordsEn: any
    metaKeywordsAr: any
    metaCanonicalUrl: any
    metaCanonicalUrlAr: any
}

const imageUrl = import.meta.env.VITE_ASSET_URL

export default function ManageCategory() {
    const navigate = useNavigate()
    const params = useParams()
    const [catId, setCatId] = useState("")
    const [isRoot, setIsRoot] = useState(false)

    const [bannerFiles, setbannerFiles] = useState<any>([])
    const [categoryFile, setcategoryFile] = useState<any>([])
    const [hoverFile, sethoverFile] = useState<any>([])
    const [bannerEror, setBannerError] = useState<any>(null)
    const [categoryError, setCategoryError] = useState<any>(null)
    const [hoverError, setHoverError] = useState<any>(null)
    const [subPop, setSubPop] = useState(false)
    const [subCats, setSubCats] = useState<any>([])
    const [subId, setSubId] = useState(null)

    const [triger, setTrigger] = useState(false)
    const [id, setId] = useState<any>("")
    const [parentId, setParentId] = useState<any>(null)

    const [childCats, setChildCats] = useState<any>([])

    const [ogImageFile, setOgImageFile] = useState([] as any)
    const [ogImageError, setOgImageError] = useState<any>(null)

    const breadcrumbItems = [
        { title: 'Categories', url: '/catalog/categories' },
        { title: 'Manage Categories', url: '' },
    ]

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    useEffect(() => {
        if (params.id) {
            api.get(endpoints.categoryDetail + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        const data = res?.data?.result
                        api.get(endpoints.getChildCategories + data?._id).then((res) => {
                            setChildCats(res.data.result)
                            console.log(res.data.result)
                        })
                        setId(data?._id)
                        setCatId(data?.categoryId)
                        setSubCats(data.subCategory)
                        setValue('nameEn', data?.name?.en)
                        setValue('nameAr', data?.name?.ar)
                        // setValue('isRoot', { value: data?.isRoot, label: data?.isRoot ? 'True' : 'False' })
                        setValue('isActive', {
                            value: data?.isActive,
                            label: data?.isActive ? 'True' : 'False',
                        })
                        setValue('topCategory', {
                            value: data?.topCategory,
                            label: data?.topCategory ? 'True' : 'False',
                        })

                        setValue('isCashbackEnabled', {
                            value: data?.isCashbackEnabled,
                            label: data?.isCashbackEnabled ? 'True' : 'False',
                        })
                        setValue('metaTitleEn', data?.seoDetails?.title?.en)
                        setValue('metaTitleAr', data?.seoDetails?.title?.ar)
                        setValue('metaDescriptionEn', data?.seoDetails?.description?.en)
                        setValue('metaDescriptionAr', data?.seoDetails?.description?.ar)
                        setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en)
                        setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar)
                        setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en)
                        setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar)
                        setOgImageFile([imageUrl + data?.seoDetails?.ogImage])
                        setValue('cashbackPercentage', data?.cashbackPercentage)

                        handleIsRootChange(data?.isRoot ? 'true' : 'false')
                        // if (data?.parent) setValue('parentCategory', { value: data?.parent?._id, label: data?.parent?.name?.en })
                        setcategoryFile([imageUrl + data?.image])
                        setbannerFiles([imageUrl + data?.banner])
                        sethoverFile([imageUrl + data?.hoverImage])
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
        }
    }, [triger])

    const {
        handleSubmit,
        setValue,
        getValues,
        register,
        control,
        formState: { errors },
    } = useForm<Inputs>()

    const onSubmit = handleSubmit(async (data: any) => {
        if (categoryFile.length == 0) {
            setCategoryError('Category Image is required')
            return
        } else setCategoryError(null)

        if (bannerFiles.length == 0) {
            setBannerError('Banner Image is required')
            return
        } else setBannerError(null)

        if (hoverFile.length == 0) {
            setHoverError('Hover Image is required')
            return
        } else setHoverError(null)

        if (ogImageFile.length == 0) {
            setOgImageError('OG Image is required')
            return
        } else setOgImageError(null)

        const formData = new FormData()
        const name = {
            en: data.nameEn?.trim(),
            ar: data.nameAr?.trim(),
        }

        formData.append('name[en]', name.en)
        formData.append('name[ar]', name.ar)
         formData.append('seoDetails[title][en]', data.metaTitleEn)
            formData.append('seoDetails[title][ar]', data.metaTitleAr)
            formData.append('seoDetails[description][en]', data.metaDescriptionEn)
            formData.append('seoDetails[description][ar]', data.metaDescriptionAr)
            formData.append('seoDetails[keywords][en]', data.metaKeywordsEn)
            formData.append('seoDetails[keywords][ar]', data.metaKeywordsAr)
            formData.append('seoDetails[canonical][en]', data.metaCanonicalUrl)
            formData.append('seoDetails[canonical][ar]', data.metaCanonicalUrlAr)
            formData.append('ogImage', ogImageFile[0])
        // formData.append('isRoot', data?.isRoot?.value);

        // if (data.parentCategory) formData.append('parent', data?.parentCategory?.value);

        if (categoryFile.length > 0) formData.append('image', categoryFile[0])

        if (bannerFiles.length > 0) formData.append('banner', bannerFiles[0])

        if (hoverFile.length > 0) formData.append('hoverImage', hoverFile[0])

        if (params.id) {
            formData.append('refid', params.id)
            formData.append('isActive', data?.isActive?.value)
            formData.append('topCategory', data?.topCategory?.value ?? "false")
            formData.append('categoryId', catId)

            if (data?.isCashbackEnabled?.value) formData.append('isCashbackEnabled', data?.isCashbackEnabled?.value);
            formData.append('cashbackPercentage', data?.cashbackPercentage)

            api.post(endpoints.updateCategory, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/catalog/categories')
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
        } else {
            api.post(endpoints.createCategory, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/catalog/categories')
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
        }
    })

    const handleIsRootChange = (value: any) => {
        setIsRoot(value === 'false')
        setValue('parentCategory', null)
    }

    const handleBannerUpload = (files: any) => {
        setbannerFiles(files)
    }

    const handleImageUpload = (files: any) => {
        setcategoryFile(files)
    }

    const handleHoverImage = (files: any) => {
        sethoverFile(files)
    }

    const handleOgImageUpload = (files: any) => {
        setOgImageFile(files)
    }

    const deleteSubCategory = (id: string, isChild: any, subParent: any) => {
        api.put(endpoints.deleteSubCategory + id, { parentRefId: params.id, isChild, subParent })
            .then((res) => {
                if (res?.status == 200) {
                    setTrigger((prev: any) => !prev)
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    return (
        <form onSubmit={onSubmit}>
            <h3>{params.id ? 'Edit' : 'Add'} Categories</h3>
            <Breadcrumb items={breadcrumbItems} />
            <h4>ID: {catId}</h4>
            <h5 className="mt-4">Name</h5>

            <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                    <label
                        className={`block form-label mb-2 ${errors.nameEn ? 'error' : ''
                            }`}
                        htmlFor="nameEn"
                    >
                        English
                    </label>
                    <input
                        type="text"
                        id="nameEn"
                        placeholder="Enter name in English....."
                        className={`input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600 ${errors.nameEn
                            ? ' focus:ring-red-600 focus-within:ring-red-600 focus-within:border-red-600 focus:border-red-600'
                            : ''
                            }`}
                        {...register('nameEn', {
                            required: 'Required Field',
                        })}
                    />
                    {errors.nameEn && (
                        <small className="text-red-600 py-3">
                            {errors.nameEn.message as string}
                        </small>
                    )}
                </div>
                <div>
                    <label
                        className={`block form-label mb-2 ${errors.nameAr ? 'error' : ''
                            }`}
                        htmlFor="nameAr"
                    >
                        Arabic
                    </label>
                    <input
                        dir="rtl"
                        type="text"
                        id="nameAr"
                        placeholder="Enter name in Arabic....."
                        className={`input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600 ${errors.nameAr
                            ? ' focus:ring-red-600 focus-within:ring-red-600 focus-within:border-red-600 focus:border-red-600'
                            : ''
                            }`}
                        {...register('nameAr', {
                            // required: 'Required Field',
                        })}
                    />
                    {/* {errors.nameAr && (
            <small className="text-red-600 py-3">
              {errors.nameAr.message as string}
            </small>
          )} */}
                </div>
            </div>

            {/* <div className="grid grid-cols-2 gap-4 mt-3">
        <FormItem label="Root Category">
          <Controller
            name="isRoot"
            control={control}
            defaultValue=""
            rules={{
              required: true,
            }}
            render={({ field }) => (
              <Select
                {...field}
                options={options}
                onChange={(option: any) => {
                  field.onChange(option);
                  handleIsRootChange(option.value);
                }}
              />
            )}
          />
          {errors.isRoot && (<small className="text-red-600 py-3"> {errors.isRoot.message as string}</small>)}

        </FormItem>
        {isRoot ? (
          <FormItem label="Parent Category">
            <Controller
              name="parentCategory"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <Select
                  {...field}
                  options={parentCategories.map((category: any) => ({
                    value: category._id,
                    label: category.name?.en,
                  }))}
                  onChange={(option: any) => {
                    field.onChange(option);
                  }}
                />
              )}
            />
          </FormItem>
        ) : null}
      </div> */}

            <div className="grid grid-cols-2 gap-4 mt-2">
                {params.id && (
                    <FormItem label="Is Active?">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select {...field} options={options} />
                            )}
                        />
                    </FormItem>
                )}

                <FormItem label="Top Category">
                    <Controller
                        name="topCategory"
                        control={control}
                        defaultValue={options[1]}
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select options={options} {...field} />
                        )}
                    />
                    {errors.topCategory && (
                        <small className="text-red-600 py-3">
                            {errors.topCategory.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-2">
                <FormItem label="Cashback">
                    <Controller
                        name="isCashbackEnabled"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select {...field} options={options} />
                        )}
                    />
                </FormItem>
                <FormItem label="Cashback Percentage">
                    <Controller
                        name="cashbackPercentage"
                        control={control}
                        defaultValue="0"
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input min={0} type="number" {...field} />
                        )}
                    />
                    {errors.cashbackPercentage && (
                        <small className="text-red-600 py-3">
                            {errors.cashbackPercentage.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="mt-2">
                <FormItem label="Category Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={categoryFile}
                        onChange={handleImageUpload}
                        ratio={[360, 180]}
                    />
                    {categoryError && (
                        <small className="text-red-600">{categoryError}</small>
                    )}
                </FormItem>
            </div>
            <div>
                <FormItem label="Banner Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={bannerFiles}
                        onChange={handleBannerUpload}
                        ratio={[1332, 240]}
                    />
                    {bannerEror && (
                        <small className="text-red-600">{bannerEror}</small>
                    )}
                </FormItem>
            </div>
            <div>
                <FormItem label="Hover Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={hoverFile}
                        onChange={handleHoverImage}
                        ratio={[360, 180]}
                    />
                    {hoverError && (
                        <small className="text-red-600">{hoverError}</small>
                    )}
                </FormItem>
            </div>

            {params.id && <div className="flex flex-col gap-4 w-full">
                <p className='form-label'>Sub Category</p>
                {childCats?.length > 0 ? <div className="flex flex-col gap-2 w-full">
                    {childCats?.map((item: any, i: number) => <RenderSubCats root={id} key={item?._id} item={item} setSubId={setSubId} setParentId={setParentId} setSubPop={setSubPop} setTrigger={setTrigger} />)}
                    {/* {subCats?.map((item: any) => (
                        <div className="w-full flex gap-4 px-4 py-2 rounded border-2 justify-between">
                            <p>{item.name.en}</p>
                            <div className="flex gap-4">
                                <HiPencilSquare
                                onClick={()=>{setSubId(item.refid);setSubPop(true)}}
                                size={25} className='cursor-pointer' />
                                <MdDeleteOutline
                                onClick={()=>deleteSubCategory(item.refid)}
                                size={25} className='cursor-pointer' />
                                <MdAddCircleOutline onClick={()=>console.log(item)} size={25} className='cursor-pointer' />
                            </div>
                        </div>
                    ))} */}
                </div> : ""}
                <Button
                    className="float-right mt-4 w-fit"
                    variant="solid"
                    onClick={() => { setSubPop(true); }}
                    type='button'
                >
                    Add sub category
                </Button>
            </div>}

            <Dialog isOpen={subPop} onClose={() => setSubPop(false)} height="80%" >
                <ManageSubCategory
                    setSubCats={setSubCats}
                    handleClose={() => {
                        setSubPop(false);
                    }}
                    setTrigger={() => { setTrigger((prev: any) => !prev) }}
                    id={null}
                    root={id}
                    parent={id}
                />
            </Dialog>


            <h5 className='mt-4'>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
               <FormItem label="Meta OG Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={ogImageFile}
                        onChange={handleOgImageUpload}
                        ratio={[1126, 1200]}
                    />
                    {ogImageError && <small className="text-red-600">{ogImageError}</small>}
                </FormItem>
                
            </div>



            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}

function RenderSubCats({ item, setSubId, setParentId, root, setTrigger }: any) {
    // Base rendering for the item
    const [childCats, setChildCats] = useState<any>([])
    const [subPop, setSubPop] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [itemToDelete, setItemToDelete] = useState(null)
    const [id, setId] = useState<any>(null)

    const getCategories = () => {
        api.get(endpoints.getChildCategories + item?._id).then((res) => {
            setChildCats(res.data.result)
        })
    }

    useEffect(() => {
        getCategories()
    }, [])

    const openDeleteModal = (refid: any) => {
        setDeleteModalOpen(true)
        setItemToDelete(refid)
    }

    const closeDeleteModal = () => {
        setDeleteModalOpen(false)
    }

    const confirmDelete = () => {
        api.put(endpoints.deleteCategory + itemToDelete)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    closeDeleteModal()
                    setTrigger((prev: any) => !prev)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                closeDeleteModal()
            })
    }

    return (
        <div className="w-full flex flex-col gap-2">
            <div key={item?._id} className="w-full flex gap-4 px-4 py-2 rounded border-2 justify-between">
                <p>{item?.name?.en}</p>
                <div className="flex gap-4">
                    <HiPencilSquare
                        onClick={() => {
                            setId(item?.refid)
                            setSubPop(true);
                        }}
                        size={25} className='cursor-pointer' />
                    <MdDeleteOutline
                        onClick={() => openDeleteModal(item?.refid)}
                        size={25} className='cursor-pointer' />
                    <MdAddCircleOutline
                        onClick={() => {
                            console.log(root)
                            console.log(item?._id)
                            setId(null)
                            setSubPop(true)
                        }}
                        size={25} className='cursor-pointer' />
                </div>
            </div>
            <div className="ml-8">
                {childCats?.map((child: any, i: number) => <RenderSubCats root={root} parent={item?._id} key={item?._id} item={child} setSubId={setSubId} setParentId={setParentId} setSubPop={setSubPop} setTrigger={() => { getCategories(); setTrigger((prev: any) => !prev) }} />)}
            </div>
            <Dialog isOpen={subPop} onClose={() => setSubPop(false)} height="80%" >
                <ManageSubCategory
                    setSubCats={setChildCats}
                    handleClose={() => {
                        setId(null);
                        setSubPop(false);
                    }}
                    setTrigger={() => { getCategories(); setTrigger((prev: any) => !prev) }}
                    root={root}
                    parent={item?._id}
                    id={id}
                />
            </Dialog>
            <DeleteModal
                isOpen={deleteModalOpen}
                title="Delete Category"
                content="Are you sure you want to delete this category?"
                onClose={closeDeleteModal}
                onConfirm={confirmDelete}
            />
        </div>
    );
}