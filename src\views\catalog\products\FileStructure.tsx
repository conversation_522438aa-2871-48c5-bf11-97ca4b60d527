import { Button } from '@/components/ui'
import { BiDownload } from 'react-icons/bi'

/**
 * v0 by Vercel.
 * @see https://v0.dev/t/zdKN578DmAV
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */
export default function FileStructure({
    type,
}: {
    type: 'Products' | 'Categories' | 'Customers' | 'Contact Lens'
}) {
    return (
        <div className="w-full">
            <h5 className="text-lg text-gray-500 pt-3">
                Upload a {type === 'Customers' ? 'CSV ' : 'zip '}file in this
                format
            </h5>
            {type === 'Products' && (
                <div className="flex items-start p-4 w-full border border-dashed mb-3">
                    <div className="grid w-full max-w-md gap-2">
                        <div className="flex items-center gap-2">
                            <FolderIcon className="w-4 h-4" />
                            <div className="font-medium">Uploads.zip</div>
                            <DownloadBtn link="upload.zip" />
                        </div>
                        <div className="grid ml-6 gap-2">
                            <div className="flex items-center gap-2">
                                <FileIcon className="w-4 h-4" />
                                <div>products.csv</div>
                                <DownloadBtn link="products.csv" />
                            </div>
                            {/* <div className="flex items-center gap-2">
                                <FileIcon className="w-4 h-4" />
                                <div>size_details.csv</div>
                                <DownloadBtn link="size_details.csv" />
                            </div>
                            <div className="flex items-center gap-2">
                                <FileIcon className="w-4 h-4" />
                                <div>technical_details.csv</div>
                                <DownloadBtn link="technical_details.csv" />
                            </div> */}
                        </div>
                        <div className="grid ml-6 gap-2">
                            <div className="flex items-center gap-2">
                                <FolderIcon className="w-4 h-4" />
                                <div className="font-medium">images</div>
                            </div>
                            <div className="grid ml-6 gap-2">
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>sunglass.webp</div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>product.jpg</div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>anyname.png</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            {type === 'Categories' && (
                <div className="flex items-start p-4 w-full border border-dashed mb-3">
                    <div className="grid w-full max-w-md gap-2">
                        <div className="flex items-center gap-2">
                            <FolderIcon className="w-4 h-4" />
                            <div className="font-medium">Uploads.zip</div>
                            <DownloadBtn link="categories.zip" />
                        </div>
                        <div className="grid ml-6 gap-2">
                            <div className="flex items-center gap-2">
                                <FileIcon className="w-4 h-4" />
                                <div>categories.csv</div>
                                <DownloadBtn link="categories.csv" />
                            </div>
                        </div>
                        <div className="grid ml-6 gap-2">
                            <div className="flex items-center gap-2">
                                <FolderIcon className="w-4 h-4" />
                                <div className="font-medium">images</div>
                            </div>
                            <div className="grid ml-6 gap-2">
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>category1.webp</div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>category2.jpg</div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>anyname.png</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            {type === 'Contact Lens' && (
                <div className="flex items-start p-4 w-full border border-dashed mb-3">
                    <div className="grid w-full max-w-md gap-2">
                        <div className="flex items-center gap-2">
                            <FolderIcon className="w-4 h-4" />
                            <div className="font-medium">Uploads.zip</div>
                            <DownloadBtn type="Contact Lens" link="upload.zip" />
                        </div>
                        <div className="grid ml-6 gap-2">
                            <div className="flex items-center gap-2">
                                <FileIcon className="w-4 h-4" />
                                <div>products.csv</div>
                                <DownloadBtn type="Contact Lens" link="products.csv" />
                            </div>
                            <div className="flex items-center gap-2">
                                <FileIcon className="w-4 h-4" />
                                <div>technical_details.csv</div>
                                <DownloadBtn type="Contact Lens" link="technical_details.csv" />
                            </div>
                        </div>
                        <div className="grid ml-6 gap-2">
                            <div className="flex items-center gap-2">
                                <FolderIcon className="w-4 h-4" />
                                <div className="font-medium">images</div>
                            </div>
                            <div className="grid ml-6 gap-2">
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>sunglass.webp</div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>product.jpg</div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <FileIcon className="w-4 h-4" />
                                    <div>anyname.png</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}

function FileIcon(props: any) {
    return (
        <svg
            {...props}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        >
            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
            <polyline points="14 2 14 8 20 8" />
        </svg>
    )
}

function FolderIcon(props: any) {
    return (
        <svg
            {...props}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        >
            <path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z" />
        </svg>
    )
}

function DownloadBtn({ link , type }: any) {
    return (
        <a
            href={ type === 'Contact Lens' ? '/data/' + link  : '/downloads/' + link}
            title="Download sample file"
            download
            className="flex items-center gap-1 bg-indigo-100 px-3 rounded-full text-xs text-indigo-600 py-1"
        >
            <BiDownload /> sample
        </a>
    )
}
