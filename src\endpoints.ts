const endpoints = {
    //BRAND ENDPOINTS
    brands: 'get-brands',
    createBrand: 'create-brand',
    updateBrand: 'update-brand',
    brandDetail: 'brand-details/',
    brandInHome: 'brand-inHome/',
    deleteBrand: 'delete-brand/',

    //CMS ENDPOINTS
    createTerms: 'create-terms',
    getTerms: 'get-terms',
    updateTerms: 'update-terms',

    createPolicy: 'create-policy',
    getPolicy: 'get-policy',
    updatePolicy: 'update-policy',

    createCookie: 'create-cookie-policy',
    getCookie: 'get-cookie-policy',
    updateCookie: 'update-cookie-policy',

    createAbout: 'create-about',
    getAbouts: 'get-abouts',
    updateAbout: 'update-about',

    refundPolicy: 'refund-policy',
    returnPolicy: 'return-policy',
    shippingPolicy: 'shipping-policy',

    //CATEGORY ENDPOINTS
    categories: 'get-categories',
    createCategory: 'create-category',
    createSubCategory: 'create-sub-category',
    updateCategory: 'update-category',
    updateSubCategory: 'update-sub-category/',
    deleteSubCategory: "delete-sub-category/",
    categoryDetail: 'category-details/',
    getChildCategories: 'get-child-categories/',
    subCategoryDetail: 'sub-category-details/',
    getSubCategory: 'sub-category/',
    parentCategories: 'parent-category',
    updateInHome: 'update-inHome/',
    deleteCategory: 'delete-category/',
    importCategories: 'import-categories',
    updateCategoryOrder: 'update-category-order',
    bulkDeleteOrders: 'bulk-delete-orders',

    //AUTH ENDPOINTS
    login: 'admin-login',
    imageUpload: 'image-upload',
    mediaDelete: 'media-delete',
    multipleUpload: 'multiple-upload',

    //ADMIN ENDPOINTS
    creatAdmin: 'create-admin',
    getAdmins: 'get-admins',
    updateAdmin: 'update-admin',
    adminDetail: 'admin-details/',
    deleteAdmin: 'delete-admin/',
    getRole: 'get-roles',
    roles: 'roles/',
    deleteRoles: 'delete-roles/',
    checkPermission: 'check-permission',
    getUser: 'get-user',

    //CUSTOMER ENDPOINTS
    customers: 'get-customers',
    createCustomer: 'create-customer',
    updateCustomer: 'update-customer',
    customerDetail: 'customer-details/',
    customerImport: 'customer-import',
    customerExport: 'customer-export',

    //PRODUCT ENDPOINTS
    exportProducts: 'export-csv',
    products: 'get-products',
    searchProducts: 'search-products',
    createProduct: 'create-product',
    updateProduct: 'update-product',
    productDetail: 'product-details/',
    getVariant: 'get-variants/',
    removeVariant: 'remove-variant/',
    addExistingVariant: 'add-existing-variant',
    videoUpload: 'video-upload',
    importProducts: 'product-import',
    reviews: 'get-reviews',
    updateReview: 'update-review/',
    deleteReview: 'delete-review/',
    createContactlenses: 'create-contact-lenses',
    updateContactlenses: 'update-contact-lenses',
    contactLensImport: 'contactLens-import',
    variantColors: 'variant-colors/',
    deleteProduct: 'delete-product/',
    duplicateProduct: "create-duplicate/",

    //STORE ENDPOINTS
    stores: 'get-stores',
    createStore: 'create-store',
    updateStore: 'update-store',
    storeDetail: 'get-store/',
    deleteStore: 'delete-store/',
    storeFilters: 'get-store-filters',

    //ATTRIBUTE ENDPOINTS
    createAttribute: 'create-attribute',
    updateAttribute: 'update-attribute/',
    attributes: 'get-attributes',
    attributeDetail: 'attribute-details/',
    attributeValue: 'attribute-value',

    //AGE GROUP ENDPOINTS
    createAgeGroup: 'create-age-group',
    updateAgeGroup: 'update-age-group',
    ageGroups: 'get-age-groups',
    ageGroupDetail: 'age-group-details/',
    deleteAgeGroup: 'delete-age-group/',

    //BLOGS ENDPOINTS
    createBlog: 'create-blog',
    blogs: 'get-blogs',
    updateBlog: 'update-blog',
    blogDetail: 'blog-details/',
    deleteBlog: 'delete-blog/',

    //COLLECTION ENDPOINTS
    createCollection: 'create-collection',
    collections: 'get-collections',
    updateCollection: 'update-collection/',
    collectionDetail: 'collection-details/',
    deleteCollection: 'delete-collection/',

    //CONTACT ENDPOINTS
    getContactUs: 'get-contact-us',
    addContactBanner: 'add-contact-banner',
    contactBanner: 'get-contact-banner',
    updateContactBanner: 'update-contact-banner',

    //INSURANCE ENDPOINTS
    insuranceEnquiries: 'insurance-enquiries',
    addProvider: 'add-insurance-provider',
    updateProvider: 'update-provider/',
    providers: 'get-insurance-providers',
    providerDetail: 'provider-details/',
    addContent: 'add-insurance-content',
    contents: 'get-insurance-content',
    updateContent: 'update-insurance-content',
    deleteProvider: 'delete-provider/',

    //FAQ ENDPOINTS
    createFaq: 'create-faq',
    updateFaq: 'update-faq',
    faqs: 'get-faqs/',

    //HOME REARRANGE
    saveHomeOrder: 'create-home-order',
    homeOrder: 'get-home-order',
    updateHomeOrder: 'update-home-order/',
    addWidget: 'add-widget/',
    setBanner: 'set-banner',
    setSingleCollection: 'set-single-collection/',
    setMultipleCollections: 'set-multiple-collection',
    getSelectedCollections: 'selected-collections',
    setSlider: 'set-slider',
    beforeAfter: 'before-after',
    widgetStatus: 'widget-status',

    //IMAGEMAP ENDPOINTS
    imageMap: 'imageMap',
    updateImageMap: 'imageMap',

    //CART ENDPOINTS
    cart: 'get-cart',
    cartDetail: 'get-cart-details/',
    exportCart: 'export-cart',

    //HEADER MENU ENDPOINTS
    createMenu: 'create-menu',
    menus: 'get-menu',
    menuDetail: 'menu-details/',
    updateMenu: 'update-menu/',
    deleteMenu: 'delete-menu/',

    //GENERAL SETTINGS ENDPOINTS
    createSettings: 'create-settings',
    settings: 'general-settings',
    updateSettings: 'update-settings',

    //SUBSCRIPTION PLANS ENDPOINTS
    createPlan: 'create-plan',
    plans: 'get-plans',
    updatePlan: 'update-plan/',
    planDetail: 'get-plan/',
    activePlans: 'active-plans',
    deletePlan: 'delete-plan/',
    getSubscriptions: 'get-subscriptions',

    //BANNER ENDPOINTS
    addBanner: 'add-banner',
    getBanners: 'get-banners',
    bannerDetail: 'banner-details/',
    updateBanner: 'update-banner/',

    //FRAME TYPE ENDPOINTS
    createFrameType: 'create-frame-type',
    frameTypes: 'get-frame-types',
    updateFrameType: 'update-frame-type',
    frameTypeDetail: 'frame-type-details/',
    activeFrameType: 'active-frameTypes',
    deleteFrameType: 'delete-frame-type/',

    //FRAME SHAPE ENDPOINTS
    createFrameShape: 'create-frame-shape',
    frameShapes: 'get-frame-shapes',
    updateFrameShape: 'update-frame-shape',
    frameShapeDetail: 'frame-shape-details/',
    activeFrameShape: 'active-frameShapes',
    deleteFrameShape: 'delete-frame-shape/',

    //TRY CART ENDPOINTS
    createTryCart: 'create-trycart',
    getTryCart: 'get-trycart',
    updateTryCart: 'update-trycart',

    //ORDER ENDPOINTS
    orders: 'get-orders',
    orderDetail: 'order-details/',
    updateOrder: 'update-order/',
    exportOrders: 'orders/export-CSV/',

    //COLOR ENDPOINTS
    createColor: 'create-color',
    colors: 'get-colors',
    colorDetail: 'color-details/',
    updateColor: 'update-color/',
    deleteColor: 'delete-color/',

    //SIZE ENDPOINTS
    createSize: 'create-size',
    createContactSize: 'create-contact-size',
    sizes: 'get-sizes',
    contactSizes: 'get-contact-sizes',
    sizeDetail: 'size-details/',
    contactSizeDetail: 'contact-size-details/',
    updateSize: 'update-size/',
    updateContactSize: 'update-contact-size/',
    deleteSize: 'delete-size/',

    //CONTACTLENS ENDPOINTS
    createContactLens: 'create-contact-lens',
    contactLens: 'get-contact-lens',
    updateContactLens: 'update-contact-lens',
    contactLensPower: 'contactLens-power/',
    contactLensDetail: 'contactLens-powers/',
    deleteContactLensPower: 'delete-contactLens-power/',

    //LENS_BRANDS ENDPOINTS
    createLensBrand: 'create-lens-brand',
    lensBrands: 'get-lens-brands',
    lensBrandDetail: 'lens-brand-details/',
    updateLensBrand: 'update-lens-brand/',
    deleteLensBrand: 'delete-lens-brand/',

    //LENS POWER ENDPOINTS
    createLensPower: 'create-lens-power',
    lensPowers: 'get-lens-powers/',
    lensPowerDetail: 'lens-power-details/',
    updateLensPower: 'update-lens-power/',
    deleteLensPower: 'delete-lens-power/',

    //LENS TYPE ENDPOINTS
    createLensType: 'create-lens-type',
    lensTypes: 'get-lens-types',
    lensTypeDetail: 'lens-type-details/',
    updateLensType: 'update-lens-type/',
    deleteLensType: 'delete-lens-type/',

    //LENS INDEX ENDPOINTS
    createLensIndex: 'create-lens-index',
    lensIndex: 'get-lens-index',
    lensIndexDetail: 'lens-index-details/',
    updateLensIndex: 'update-lens-index/',
    deleteLensIndex: 'delete-lens-index/',

    //LENS COATING ENDPOINTS
    createLensCoating: 'create-coating',
    lensCoating: 'get-coatings',
    lensCoatingDetail: 'coating-details/',
    updateLensCoating: 'update-coating/',
    deleteLensCoating: 'delete-coating/',

    //NEWS LETTER ENDPOINTS
    newsletter: 'get-newsletter',
    addNewsletter: 'newsletter',
    deleteNewsletter: 'newsletter/',
    unSubscribeNewsletter: 'newsletter-unsubscribe/',

    //COUPON ENDPOINTS
    createCoupon: 'create-coupon',
    coupons: 'get-coupons',
    couponDetail: 'coupon-details/',
    updateCoupon: 'update-coupon/',

    //LABEL ENDPOINTS
    createLabel: 'create-label',
    labels: 'label/',
    deletelabel: 'delete-label/',

    //ADDRESS ENDPOINTS
    addressDetail: 'address-details',
    updateAddress: 'update-address/',

    //FOOTER ENDPOINTS
    footer: 'footer/',

    //DASHBOARD ENDPOINTS
    statisticData: 'statisticData',
    latestOrderData: 'latestOrderData',
    topSellingData: 'topSellingData',
    salesByCategories: 'salesByCategories',
    salesReportData: 'salesReport',

    //REPORTS ENDPOINTS
    salesReport: 'sales-report',
    revenueReport: 'revenue-report',
    topSellingReport: 'top-selling-report',
    customerReport: 'customer-report',
    guestReport: 'guest-report',

    //NON LIST ENDPOINTS
    nonList: 'non-list/',
    productEnquiries: 'product-enquiries',
    lensEnquiries: 'lens-enquiries',
    deleteNonList: 'delete-non-list/',

    //META TAG ENDPOINTS
    metaTags: 'meta-tag',

    //FRONT MATERIAL ENDPOINTS
    frontMaterials: 'front-materials/',
    deleteFrontMaterial: 'delete-front-material/',

    //TYPE ENDPOINTS
    types: 'types/',
    deleteTypes: 'delete-types/',

    //LENS MATERIAL ENDPOINTS
    lensMaterials: 'lens-materials/',
    deleteLensMaterial: 'delete-lens-material/',

    //PAYMEN METHOD FEE ENDPOINTS
    paymentMethodFees: "get-payment-method-fees/",
    updatePaymnetMethodFees: "update-payment-method-fee/",

    // VM POLICY
    VMPolicy: "vm-policy/",

    //texts
    translation: "translation",

    enquiryDetails: "enquiry-details"


}

export default endpoints
