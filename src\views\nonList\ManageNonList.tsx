/* eslint-disable */
import { Button, FormItem, Input, Select, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

export default function ManageSize() {
    const [stores, setStores] = useState<any>([])
    const navigate = useNavigate()
    const params = useParams()

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const breadcrumbItems = [
        { title: 'Non List Items', url: '/non-list' },
        {
            title: params.id ? 'Edit Non List Item' : 'Create Non List Item',
            url: '',
        },
    ]

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const getStores = () => {
        api.get(endpoints.stores).then((res) => {
            if (res.status == 200) {
                const stores = res.data?.result || []
                const newOptions = stores.map((store: any) => ({
                    value: store._id,
                    label: store.name.en,
                }))
                setStores(newOptions)
            }
        })
    }

    useEffect(() => {
        if (params.id) {
            api.get(endpoints.nonList + params.id)
                .then((res) => {
                    if (res.status == 200) {
                        const data = res.data?.result
                        setValue('name', data?.name.en)
                        setValue('nameAr', data?.name.ar)
                        setValue(
                            'store',
                            data?.stores.map((store: any) => ({
                                value: store._id,
                                label: store.name?.en,
                            }))
                        )
                        setValue('isActive', {
                            value: data?.isActive.toString(),
                            label: data?.isActive ? 'True' : 'False',
                        })
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
        getStores()
    }, [params.id])

    const onSubmit = (data: any) => {
        const values: any = {
            name: {en: data?.name, ar: data?.nameAr},
            stores: data?.store?.map((store: any) => store.value),
        }

        if (params.id) {
            values.isActive = data?.isActive?.value == 'true' ? true : false

            api.put(endpoints.nonList + params.id, values)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/non-list')
                    }
                })
                .catch((err) => {
                    toast.push(<Notification type="warning" title={err} />, {
                        placement: 'top-center',
                    })
                })
        } else {
            api.post(endpoints.nonList, values)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/non-list')
                    }
                })
                .catch((err) => {
                    toast.push(<Notification type="warning" title={err} />, {
                        placement: 'top-center',
                    })
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">
                {params.id ? 'Edit' : 'Add '} Non List Item
            </h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Name">
                    <Controller
                        name="name"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.name && (
                        <small className="text-red-600 py-3">
                            {errors.name.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Name Arabic">
                    <Controller
                        name="nameAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Name is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.nameAr && (
                        <small className="text-red-600 py-3">
                            {errors.nameAr.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Stores">
                    <Controller
                        name="store"
                        control={control}
                        defaultValue={[]}
                        rules={{ required: 'Store is required' }}
                        render={({ field }) => (
                            <Select isMulti options={stores} {...field} />
                        )}
                    />
                    {errors.store && (
                        <small className="text-red-600 py-3">
                            {errors.store.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                {params.id && (
                    <FormItem label="Is Active?">
                        <Controller
                            name="isActive"
                            control={control}
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                    </FormItem>
                )}
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
