import { Button, FormItem, toast, Select, Upload } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

const imageUrl = import.meta.env.VITE_ASSET_URL

/* eslint-disable */
export default function ManageFrameShape() {
    const navigate = useNavigate()
    const params = useParams()
    const [imageFile, setImageFile] = useState<any>([])
    const [imageError, setImageError] = useState<any>(null)

    const breadcrumbItems = [
        { title: 'Frame Shapes', url: '/frame-shapes' },
        { title: 'Manage Frame Shape', url: '' },
    ]

    const activeOptions: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const handleImageUpload = (files: any) => {
        setImageFile(files)
    }

    const {
        handleSubmit,
        setValue,
        control,
        formState: { errors },
    } = useForm<any>()

    useEffect(() => {
        if (params.id)
            api.get(endpoints.frameShapeDetail + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        setValue('nameEn', res.data?.result?.name?.en)
                        setValue('nameAr', res.data?.result?.name?.ar)
                        setValue('position', res.data?.result?.position)
                        setValue('isActive', {
                            value: res.data?.result?.isActive,
                            label: res.data?.result?.isActive
                                ? 'True'
                                : 'False',
                        })
                        setImageFile([imageUrl + res.data?.result?.image])
                        console.log(res.data)
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
    }, [])

    const onSubmit = (value: any) => {
        if (imageFile.length == 0) {
            setImageError('Image is required')
            return
        }
        setImageError(null)

        const formData = new FormData()
        formData.append('name[en]', value.nameEn)
        formData.append('name[ar]', value.nameAr)
        formData.append('position', value.position)
        if (imageFile.length > 0) formData.append('image', imageFile[0])

        if (params.id) {
            formData.append('refid', params.id)
            formData.append('isActive', value.isActive.value)

            api.post(endpoints.updateFrameShape, formData)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/frame-shapes')
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        } else {
            api.post(endpoints.createFrameShape, formData)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/frame-shapes')
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params.id ? 'Edit' : 'Add'} Frame Shape </h3>
            <Breadcrumb items={breadcrumbItems} />

            <h5 className="mb-2">Name</h5>
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="English">
                    <Controller
                        name="nameEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.nameEn
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.nameEn && (
                        <small className="text-red-600 py-3">
                            {errors.nameEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="nameAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input type="text" {...field} dir="rtl" />
                        )}
                    />
                    {/* {errors.nameAr && (
                        <small className="text-red-600 py-3">
                            {errors.nameAr.message as string}
                        </small>
                    )} */}
                </FormItem>

                <FormItem label="Position">
                    <Controller
                        name="position"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="number"
                                className={`${
                                    errors.position
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.position && (
                        <small className="text-red-600 py-3">
                            {errors.position.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div>
                <FormItem label="Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={imageFile}
                        onChange={handleImageUpload}
                        ratio={[200, 80]}
                    />
                    {imageError && (
                        <small className="text-red-600">{imageError}</small>
                    )}
                </FormItem>
            </div>

            {params.id && (
                <div className="grid grid-cols-2 gap-4 mt-4">
                    <FormItem label="isActive? ">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    defaultValue={''}
                                    {...field}
                                    options={activeOptions}
                                />
                            )}
                        />
                    </FormItem>
                </div>
            )}

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
