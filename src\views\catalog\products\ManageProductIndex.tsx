import {
    <PERSON>I<PERSON>,
    Select,
    FormContainer,
    <PERSON><PERSON>,
    toast,
    Notification,
} from '@/components/ui'
import { useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import Breadcrumb from '@/views/modals/BreadCrumb'
import { useNavigate, useParams } from 'react-router-dom'
import ManageProduct from './ManageProduct'
import ManageContactLens from '@/views/contactLens/ManageContactLens'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { AiOutlineSave } from 'react-icons/ai'

export default function ManageProductIndex() {
    const params = useParams()
    const [res, setRes] = useState<any>(null)
    const [loading, setLoading] = useState(false)
    const breadcrumbItems = [
        { title: 'Products', url: '/catalog/products' },
        { title: 'Manage Product', url: '' },
    ]

    const navigate = useNavigate()

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
        getValues,
        watch
    } = useForm<any>()

    const productType = watch('productType', { label: "Frame", value: "frame" })

    useEffect(() => { 
        if (params.id) {
            api.get(endpoints.productDetail + params.id)
                .then((resp) => {
                    setValue('productType', resp?.data?.result?.productType === "frame" ? { label: "Frame", value: "frame" } : { label: "Contact Lens", value: "contactLens" })
                    setRes(resp)
                }).catch((error) => {
                    console.error('Error fetching data: ', error)
                })
        }
    }, [])

    const saveDuplicate = async  () => {
        setLoading(true)
        const product = res?.data?.result
        let data = {
            ...product,
            name: {
                en:  product.name.en + '(Duplicate)',
                ar: product.name.ar + '(Duplicate)',
            },
            sku: product.sku + '(Duplicate)',
            slug: null,
            isActive: false,
            _id: null,
            createdAt: null,
            updatedAt: null,
        }

        api.post(endpoints.duplicateProduct, data).then((res) => {
            if (res.status == 200) {
                if (res?.data?.errorCode == 0) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    navigate('/catalog/products')
                }
            } else {
                setLoading(false)
                toast.push(
                    <Notification
                        type="warning"
                        title={res.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            }
        })
    }


    return (
        <div className='flex flex-col'>
            <h3 className="mb-2">{params.type?.replace(/-/g, ' ')}</h3>

            <Breadcrumb items={breadcrumbItems} />
            {params?.id && <Button
                className="w-fit my-3 ml-auto"
                variant="solid"
                type="button"
                icon={<AiOutlineSave />}
                onClick={saveDuplicate}
                loading={loading}
            >
                Save Duplicate
            </Button>}
            <form action="">
                <FormItem label="Product Type">
                    <Controller
                        name="productType"
                        control={control}
                        defaultValue={{ label: "Frame", value: "frame" }}
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select
                                options={[{ label: "Frame", value: "frame" }, { label: "Contact Lens", value: "contactLens" }]}
                                isOptionDisabled={(option) => option.isDisabled}
                                {...field}
                            />
                        )}
                    />
                </FormItem>
            </form>
            {productType?.value === 'frame' ? (
                <ManageProduct res={res} />
            ) : (
                <ManageContactLens res={res} />
            )}
        </div>
    )
}
