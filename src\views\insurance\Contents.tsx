import { RichTextEditor } from "@/components/shared";
import Button from "@/components/ui/Button";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { useF<PERSON>, SubmitHandler, Controller } from "react-hook-form";
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import { useEffect, useState } from "react";
import { FormItem, Input, Upload } from "@/components/ui";
import Breadcrumb from "../modals/BreadCrumb";

/*eslint-disable */
export default function Contents() {
    const [isUpdate, setIsUpdate] = useState(false);
    const [formSubmitted, setFormSubmitted] = useState(false);
    const [ogImageFile, setOgImageFile] = useState([] as any)

    const imageUrl = import.meta.env.VITE_ASSET_URL

    const breadcrumbItems = [
        { title: 'Insurance Page', url: '' },
    ]

    const handleOgImageUpload = (files: any) => {
        setOgImageFile(files)
    }

    useEffect(() => {
        api.get(endpoints.contents).then((res) => {
            if (res?.status == 200) {
                if (res?.data?.result?.length > 0) {
                    const data = res?.data?.result[0]
                    setValue('pageTitleEn', data?.pageTitle?.en)
                    setValue('pageTitleAr', data?.pageTitle?.ar)
                    setValue('pageDescriptionEn', data?.pageDescription?.en)
                    setValue('pageDescriptionAr', data?.pageDescription?.ar)
                    setValue('descOneEn', data?.descriptionOne?.en)
                    setValue('descOneAr', data?.descriptionOne?.ar)
                    setValue('descTwoEn', data?.descriptionTwo?.en)
                    setValue('descTwoAr', data?.descriptionTwo?.ar)
                    setValue('termsTitleEn', data?.terms?.title?.en)
                    setValue('termsTitleAr', data?.terms?.title?.ar)
                    setValue('termsContentEn', data?.terms?.content?.en)
                    setValue('termsContentAr', data?.terms?.content?.ar)
                    setValue('metaTitleEn', data?.seoDetails?.title?.en)
                    setValue('metaTitleAr', data?.seoDetails?.title?.ar)
                    setValue('metaDescriptionEn', data?.seoDetails?.description?.en)
                    setValue('metaDescriptionAr', data?.seoDetails?.description?.ar)
                    setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en)
                    setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar)
                    setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en)
                    setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar)
                    setOgImageFile([imageUrl + data?.seoDetails?.ogImage])
                    setIsUpdate(true)
                }
            }
        })
    }, [formSubmitted])


    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>();
    
    const onSubmit: SubmitHandler<any> = (data) => {
        const formData = new FormData();
        
        // Page Title
        formData.append('pageTitle[en]', data.pageTitleEn);
        formData.append('pageTitle[ar]', data.pageTitleAr);
        
        // Page Description
        formData.append('pageDescription[en]', data.pageDescriptionEn);
        formData.append('pageDescription[ar]', data.pageDescriptionAr);
        
        // Description One
        formData.append('descriptionOne[en]', data.descOneEn);
        formData.append('descriptionOne[ar]', data.descOneAr);
        
        // Description Two
        formData.append('descriptionTwo[en]', data.descTwoEn);
        formData.append('descriptionTwo[ar]', data.descTwoAr);
        
        // Terms
        formData.append('terms[title][en]', data.termsTitleEn);
        formData.append('terms[title][ar]', data.termsTitleAr);
        formData.append('terms[content][en]', data.termsContentEn);
        formData.append('terms[content][ar]', data.termsContentAr);
        
        // SEO Details
        formData.append('seoDetails[title][en]', data.metaTitleEn);
        formData.append('seoDetails[title][ar]', data.metaTitleAr);
        formData.append('seoDetails[description][en]', data.metaDescriptionEn);
        formData.append('seoDetails[description][ar]', data.metaDescriptionAr);
        formData.append('seoDetails[keywords][en]', data.metaKeywordsEn);
        formData.append('seoDetails[keywords][ar]', data.metaKeywordsAr);
        formData.append('seoDetails[canonical][en]', data.metaCanonicalUrl);
        formData.append('seoDetails[canonical][ar]', data.metaCanonicalUrlAr);
        
        // Handle OG Image file upload
        if (ogImageFile && ogImageFile.length > 0) {
            // Check if it's a File object or a string URL
            if (typeof ogImageFile[0] === 'string' && ogImageFile[0].startsWith(imageUrl)) {
                // It's an existing image URL, extract filename
                const filename = ogImageFile[0].replace(imageUrl, '');
                formData.append('ogImage', filename);
            } else if (ogImageFile[0] instanceof File) {
                // It's a new file upload
                formData.append('ogImage', ogImageFile[0]);
            }
        }

        const config = {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        };

        if (isUpdate) {
            api.post(endpoints.updateContent, formData, config)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification type="success" title={res.data.message} />, {
                            placement: 'top-center',
                        })
                        setFormSubmitted(true);
                    }
                }).catch((err) => {
                    console.log(err);
                    toast.push(
                        <Notification type="danger" title="Error updating content" />, {
                        placement: 'top-center',
                    })
                })
        } else {
            api.post(endpoints.addContent, formData, config)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification type="success" title={res.data.message} />, {
                            placement: 'top-center',
                        })
                        setFormSubmitted(true);
                    }
                }).catch((err) => {
                    console.log(err);
                    toast.push(
                        <Notification type="danger" title="Error adding content" />, {
                        placement: 'top-center',
                    })
                })
        }
    }
    
    return (
        <form onSubmit={handleSubmit(onSubmit)} encType="multipart/form-data">
            <h4>Insurance Page Content</h4>
            <Breadcrumb items={breadcrumbItems} />

            <h5 className="mt-4">Page</h5>
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label='Title English'>
                    <Controller
                        control={control}
                        name="pageTitleEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.pageTitleEn && (<small className="text-red-600 py-3"> {errors.pageTitleEn.message as string} </small>)}
                </FormItem>
                <FormItem label='Title Arabic'>
                    <Controller
                        control={control}
                        name="pageTitleAr"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                            dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.pageTitleAr && (<small className="text-red-600 py-3"> {errors.pageTitleAr.message as string} </small>)}
                </FormItem>
            </div>
            <div>
                <FormItem label="Description English">
                    <Controller
                        name="pageDescriptionEn"
                        control={control}
                        defaultValue=""

                        rules={{
                            required: "Field is Required",
                            minLength: { value: 12, message: "Field is Required" },
                        }}
                        render={({ field }) => (
                            <RichTextEditor  {...field} />
                        )}
                    />
                    {errors.pageDescriptionEn && <small className="text-red-600 py-3">{errors.pageDescriptionEn.message as string}</small>}
                </FormItem>
            </div>
            <div>
                <FormItem label="Description Arabic">
                    <Controller
                        name="pageDescriptionAr"
                        control={control}
                        defaultValue=""

                        rules={{
                            required: "Field is Required",
                            minLength: { value: 12, message: "Field is Required" },
                        }}
                        render={({ field }) => (
                            <RichTextEditor dir="rtl"  {...field} />
                        )}
                    />
                    {errors.pageDescriptionAr && <small className="text-red-600 py-3">{errors.pageDescriptionAr.message as string}</small>}
                </FormItem>
            </div>

            <h5 className="mt-4">Description One</h5>
            <div>
                <FormItem label="English">
                    <Controller
                        name="descOneEn"
                        control={control}
                        defaultValue=""

                        rules={{
                            required: "Field is Required",
                            minLength: { value: 12, message: "Field is Required" },
                        }}
                        render={({ field }) => (
                            <RichTextEditor  {...field} />
                        )}
                    />
                    {errors.descOneEn && <small className="text-red-600 py-3">{errors.descOneEn.message as string}</small>}
                </FormItem>
            </div>

            <div>
                <FormItem label="Arabic">
                    <Controller
                        name="descOneAr"
                        control={control}
                        defaultValue=""
                        // rules={{
                        //     required: "Field is Required",
                        //     minLength: { value: 12, message: "Field is Required" },
                        // }}
                        render={({ field }) => (
                            <RichTextEditor {...field} />
                        )}
                    />
                    {/* {errors.descOneAr && <small className="text-red-600 py-3">{errors?.descOneAr?.message as string}</small>} */}

                </FormItem>
            </div>

            <h5 className="mt-4">Description Two</h5>
            <div>
                <FormItem label="English">
                    <Controller
                        name="descTwoEn"
                        control={control}
                        defaultValue=""

                        rules={{
                            required: "Field is Required",
                            minLength: { value: 12, message: "Field is Required" },
                        }}
                        render={({ field }) => (
                            <RichTextEditor  {...field} />
                        )}
                    />
                    {errors.descTwoEn && <small className="text-red-600 py-3">{errors.descTwoEn.message as string}</small>}
                </FormItem>
            </div>

            <div>
                <FormItem label="Arabic">
                    <Controller
                        name="descTwoAr"
                        control={control}
                        defaultValue=""
                        // rules={{
                        //     required: "Field is Required",
                        //     minLength: { value: 12, message: "Field is Required" },
                        // }}
                        render={({ field }) => (
                            <RichTextEditor {...field} />
                        )}
                    />
                    {/* {errors.descTwoAr && <small className="text-red-600 py-3">{errors?.descTwoAr?.message as string}</small>} */}

                </FormItem>
            </div>

            <h5 className="mt-4">Terms & condition</h5>
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label='Title English'>
                    <Controller
                        control={control}
                        name="termsTitleEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.termsTitleEn && (<small className="text-red-600 py-3"> {errors.termsTitleEn.message as string} </small>)}
                </FormItem>
                <FormItem label='Title Arabic'>
                    <Controller
                        control={control}
                        name="termsTitleAr"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                            dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.termsTitleAr && (<small className="text-red-600 py-3"> {errors.termsTitleAr.message as string} </small>)}
                </FormItem>
            </div>
            <div>
                <FormItem label="Content English">
                    <Controller
                        name="termsContentEn"
                        control={control}
                        defaultValue=""

                        rules={{
                            required: "Field is Required",
                            minLength: { value: 12, message: "Field is Required" },
                        }}
                        render={({ field }) => (
                            <RichTextEditor  {...field} />
                        )}
                    />
                    {errors.termsContentEn && <small className="text-red-600 py-3">{errors.termsContentEn.message as string}</small>}
                </FormItem>
            </div>
            <div>
                <FormItem label="Content Arabic">
                    <Controller
                        name="termsContentAr"
                        control={control}
                        defaultValue=""

                        rules={{
                            required: "Field is Required",
                            minLength: { value: 12, message: "Field is Required" },
                        }}
                        render={({ field }) => (
                            <RichTextEditor dir="rtl"  {...field} />
                        )}
                    />
                    {errors.termsContentAr && <small className="text-red-600 py-3">{errors.termsContentAr.message as string}</small>}
                </FormItem>
            </div>

             <h5 className='mt-4'>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
               <FormItem label="Meta OG Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={ogImageFile}
                        onChange={handleOgImageUpload}
                        ratio={[1126, 1200]}
                    />
                </FormItem>
                
            </div>

            <Button className='float-right mt-4' variant="solid" type="submit">
                Submit
            </Button>
        </form>
    );
}