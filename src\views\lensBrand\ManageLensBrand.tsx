/* eslint-disable */
import { Button, FormItem, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import { Dropzone } from '@/components/shared/Dropzone'
import Breadcrumb from '../modals/BreadCrumb'

export default function ManageLensBrand() {
    const [logoFile, setLogoFile] = useState<string[]>([])
    const [coverFile, setCoverFile] = useState<string[]>([])
    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Lens Brand', url: '/lens-brand' },
        { title: params?.id ? 'Update Lens Brand' : 'Create Lens Brand', url: '' },
    ];

    const getLensBrandDetail = async () => {
        api.get(endpoints.lensBrandDetail + params.id).then((res) => {
            if (res?.status == 200) {
                const data = res?.data?.result
                setValue('name', data?.name?.en)
                setValue('nameAr', data?.name?.ar)
                setLogoFile([data.logo])
                setCoverFile([data.cover])
            }
        }).catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }

    useEffect(() => {
        if (params.id) {
            getLensBrandDetail()
        }
    }, [])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit = (value: any) => {
        console.log(value)
        const data = {
            name: {en: value.name, ar: value.nameAr},
            logo: logoFile[0],
            cover: coverFile[0],
        }

        console.log("data :: ", data)

        if (params?.id) {
            api.put(endpoints.updateLensBrand + params.id, data).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/lens-brand')
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        } else {
            api.post(endpoints.createLensBrand, data).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/lens-brand')
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">
                {params?.id ? 'Update' : 'Create'} Lens Brand
            </h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Name">
                    <Controller
                        control={control}
                        name="name"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.name && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.name.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Name Arabic">
                    <Controller
                        control={control}
                        name="nameAr"
                        // rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors.name && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.name.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Logo">
                    <Dropzone
                        previews={logoFile}
                        onFileUrlChange={(e) => {
                            setLogoFile([e])
                        }}
                        ratio={[256, 100]}
                    />
                </FormItem>

                <FormItem label="Cover Image">
                    <Dropzone
                        previews={coverFile}
                        onFileUrlChange={(e) => {
                            setCoverFile([e])
                        }}
                        ratio={[472, 208]}
                    />
                </FormItem>
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                {params?.id ? 'Update' : 'Save'}
            </Button>
        </form>
    )
}
