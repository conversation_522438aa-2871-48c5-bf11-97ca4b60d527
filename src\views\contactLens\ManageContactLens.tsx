/* eslint-disable */
import {
    Button,
    FormItem,
    toast,
    Select,
    FormContainer,
    Checkbox,
    Upload,
} from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { AiOutlineMinus, AiOutlinePlus, AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { ChangeEvent, useEffect, useRef, useState } from 'react'
import Input from '@/components/ui/Input'
import { RichTextEditor } from '@/components/shared'
import Breadcrumb from '@/views/modals/BreadCrumb'
import { Dropzone } from '@/components/shared/Dropzone'
import { IoIosCloseCircleOutline } from 'react-icons/io'
import { sortPower } from '../catalog/products/ManageProduct'

const imageUrl = import.meta.env.VITE_ASSET_URL

export default function ManageContactLens({ res }: any) {
    const [brandOptions, setBrandOptions] = useState([])
    const [categoryOptions, setCategoryOptions] = useState([])
    const [productOptions, setProductOptions] = useState([])
    const [ageOptions, setAgeOptions] = useState([])
    const [planOptions, setPlanOptions] = useState([])
    const [colorOptions, setColorOptions] = useState<any>([])
    const [thumbnailFiles, setThumbnailFiles] = useState<any>([])
    const [thumbnailError, setThumbnailError] = useState<any>(null)
    const [lensImageFiles, setLensImageFiles] = useState<any>([])
    const [lensImageError, setLensImageError] = useState<any>(null)
    const [productFiles, setProductFiles] = useState<any>([])
    const [productError, setProductError] = useState<any>(null)
    const [descriptionFiles, setDescriptionFiles] = useState<any>([])
    const [descriptionError, setDescriptionError] = useState<any>(null)
    const [videoFile, setVideoFile] = useState<any>(null)
    const [videoPreview, setVideoPreview] = useState('')
    const [videoUrl, setVideoUrl] = useState<any>(null)
    const [labels, setLabels] = useState<any>([])
    const navigate = useNavigate()
    const params = useParams()
    const [sphChecked, setSphChecked] = useState(false)
    const [cylChecked, setCylChecked] = useState(false)
    const [axisChecked, setAxisChecked] = useState(false)
    const [multiFocalChecked, setMultiFocalChecked] = useState(false)
    const [isShowPercentage, setIsShowPercentage] = useState(false)
    const [sizeOptions, setSizeOptions] = useState([])
    const [customizable, setCustomizable] = useState(false)
    const [isTaxIncluded, setIsTaxIncluded] = useState(false)
    const [ogImageFile, setOgImageFile] = useState<string[]>([])

    const [subCats, setSubCats] = useState<any>([])
    const [subChildCats, setSubChildCats] = useState<any>([])

    const [sphValues, setSphValues] = useState([])
    const [cylValues, setCylValues] = useState([])
    const [axisValues, setAxisValues] = useState([])

    const genderOptions: any = [
        { value: 'Men', label: 'Men' },
        { value: 'Women', label: 'Women' },
        { value: 'Unisex', label: 'Unisex' },
        { value: 'Kids', label: 'Kids' },
    ]

    const onCheck = (checked: boolean, e: ChangeEvent<HTMLInputElement>) => {
        setCustomizable(checked)
    }


    const handleOgImageUpload = (file: any) => {
        setOgImageFile(file)
    }

    const onIsShowPercentage = (checked: boolean, e: ChangeEvent<HTMLInputElement>) => {
        setIsShowPercentage(!isShowPercentage)
    }

    const onIsTaxIncluded = (checked: boolean, e: ChangeEvent<HTMLInputElement>) => {
        setIsTaxIncluded(!isTaxIncluded)
    }

    const handleFileChange = (event: any) => {
        const file = event.target.files[0]
        const maxFileSize = 4 * 1024 * 1024
        if (file) {
            if (file.size > maxFileSize) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File size exceeds the maximum limit of 4MB."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }
            setVideoFile(file)
            const videoURL = URL.createObjectURL(file)
            setVideoPreview(videoURL)
        }
    }

    const handleVideoUpload = () => {
        const formData = new FormData()
        formData.append('video', videoFile)
        api.post(endpoints.videoUpload, formData)
            .then((res) => {
                if (res?.status == 200) {
                    setVideoUrl(res?.data?.videoUrl)
                    setVideoFile(null)
                }
            })
            .catch((error) => {
                console.error('Error uploading video: ', error)
            })
    }

    const getSphValues = () => {
        api.get(endpoints.contactLensPower + 'Sph').then((res) => {
            if (res.status == 200) {
                const powers = res?.data?.result || []
                const newPowers = powers.sort(sortPower).map(
                    (values: any) => ({
                        value: values._id,
                        label: values.name,
                    })
                )
                setSphValues(newPowers)
            }
        })
    }

    const getCylValues = () => {
        api.get(endpoints.contactLensPower + 'Cyl').then((res) => {
            if (res.status == 200) {
                const powers = res?.data?.result || []
                const newPowers = powers.sort(sortPower).map(
                    (values: any) => ({
                        value: values._id,
                        label: values.name,
                    })
                )
                setCylValues(newPowers)
            }
        })
    }

    const getAxisValues = () => {
        api.get(endpoints.contactLensPower + 'Axis').then((res) => {
            if (res.status == 200) {
                const powers = res?.data?.result || []
                const newPowers = powers.map(
                    (values: any) => ({
                        value: values._id,
                        label: values.name,
                    })
                )
                setAxisValues(newPowers.sort((a: any, b: any) => a.label - b.label))
            }
        })
    }


    const getColors = () => {
        api.post(endpoints.colors, { isActive: true }).then((res) => {
            if (res.status == 200) {
                const colors = res?.data?.result || []
                const newColorOptions = colors.map((color: any) => ({
                    value: color._id,
                    label: color.name.en,
                }))
                setColorOptions(newColorOptions)
            }
        })
    }

    const getPlans = () => {
        api.get(endpoints.activePlans).then((res) => {
            if (res.status == 200) {
                const plans = res?.data?.result || []
                const newPlanOptions = plans.map((plan: any) => ({
                    value: plan._id,
                    label: typeof plan.name == "string" ? plan.name : plan.name.en,
                }))
                console.log(newPlanOptions)
                setPlanOptions(newPlanOptions)
            }
        })
    }

    const getBrands = () => {
        api.get(endpoints.brands)
            .then((res) => {
                if (res?.status == 200) {
                    const brands = res?.data?.result || []
                    const newBrandOptions = brands.map((brand: any) => ({
                        value: brand._id,
                        label: brand.name.en,
                    }))
                    setBrandOptions(newBrandOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getCategories = () => {
        api.get(endpoints.parentCategories)
            .then((res) => {
                if (res?.status == 200) {
                    const categories = res?.data?.result || []
                    const newCategoryOptions = categories.map(
                        (category: any) => ({
                            value: category._id,
                            label: category.name.en,
                            subCategory: category.subCategory
                        })
                    )
                    setCategoryOptions(newCategoryOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getProducts = () => {
        api.post(endpoints.products, { refid: params.id })
            .then((res) => {
                if (res?.status == 200) {
                    const products = res?.data?.result?.products || []
                    const newproductOptions = products.map((product: any) => ({
                        value: product._id,
                        label: product.name.en,
                    }))
                    setProductOptions(newproductOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getAgeGroups = () => {
        api.post(endpoints.ageGroups, { isActive: true })
            .then((res) => {
                if (res?.status == 200) {
                    const ageGroups = res?.data?.result || []
                    const newAgeGroupOptions = ageGroups.map(
                        (ageGroup: any) => ({
                            value: ageGroup._id,
                            label: ageGroup.name,
                        })
                    )
                    setAgeOptions(newAgeGroupOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getLabels = () => {
        api.post(endpoints.labels, { isActive: true }).then((res) => {
            if (res?.status == 200) {
                const labels = res?.data?.result || []
                const newLabelOptions = labels.map((label: any) => ({
                    value: label._id,
                    label: label.name?.en,
                }))
                setLabels(newLabelOptions)
            }
        })
    }

    const getSizes = () => {
        api.post(endpoints.contactSizes, { isActive: true }).then((res) => {
            if (res.status == 200) {
                const sizes = res?.data?.result || []
                const newSizeOptions = sizes.map((size: any) => ({
                    value: size._id,
                    label: size.name,
                }))
                setSizeOptions(newSizeOptions)
            }
        })
    }

    const onNameChange = (e: any) => {
        api.post("generate-slug", { name: e.target.value }).then(res => {
            setValue("slug", res.data.result.slug)
        }).catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }

    const ref = useRef<any>({})

    useEffect(() => {
        getBrands()
        getCategories()
        getProducts()
        getColors()
        getPlans()
        // getAgeGroups()
        getLabels()
        getSizes()
        getSphValues()
        getCylValues()
        getAxisValues()

        if (sizeFields.length == 0) sizeAppend({})

        if (params.id) {
            if (res?.status == 200) {
                const data = res?.data?.result
                setValue('nameEn', data?.name?.en)
                setValue('nameAr', data?.name?.ar)
                setValue('weight', data?.weight)
                setValue('position', data?.position)
                setValue('slug', data?.slug)
                setValue('brand', {
                    value: data?.brand?._id,
                    label: data?.brand?.name?.en,
                })
                setValue('gender', {
                    value: data?.gender,
                    label: data?.gender,
                })
                if (data?.category?.length > 0) {
                    setValue(
                        'category',
                        data?.category?.map((category: any) => ({
                            value: category?._id,
                            label: category?.name?.en,
                            subCategory: category?.subCategory
                        }))
                    )
                }

                setValue('isCashbackEnabled', {
                    value: data?.isCashbackEnabled,
                    label: data?.isCashbackEnabled ? 'True' : 'False',
                })
                setValue('cashbackPercentage', data?.cashbackPercentage)

                let subChilds: any = []
                let third: any = []
                let catIds = data?.category?.map((category: any) => category?._id)
                data?.subCategory?.forEach((sub: any) => {
                    if (catIds.includes(sub?.parent)) {
                        subChilds.push({ value: sub?._id, label: sub?.name?.en })
                    }
                })

                if (subChilds.length > 0)
                    setValue('subCategory', subChilds?.map((category: any) => ({
                        value: category?.value,
                        label: category?.label,
                        childs: category?.childs
                    })));
                let subIds = subChilds?.map((category: any) => category?.value)
                data?.subCategory?.forEach((sub: any) => {
                    if (subIds.includes(sub?.parent)) {
                        third.push({ value: sub?._id, label: sub?.name?.en })
                    }
                })
                if (third?.length > 0)
                    setValue('subChildCategory', third?.map((category: any) => ({
                        value: category?.value,
                        label: category?.label,
                    })));
                // if (data?.ageGroup.length > 0)
                //     setValue(
                //         'ageGroup',
                //         data?.ageGroup?.map((ageGroup: any) => ({
                //             value: ageGroup?._id,
                //             label: ageGroup?.name,
                //         }))
                //     )
                if (data?.plans?.length > 0) {
                    setValue(
                        'plans',
                        data?.plans?.map((plan: any) => ({
                            value: plan?._id,
                            label: typeof plan?.name == "string" ? plan?.name : plan?.name?.en,
                        }))
                    )
                }
                setValue('sku', data?.sku)
                setValue('priceAed', data?.price?.aed)
                setValue('offerPriceAed', data?.offerPrice?.aed)
                setValue('stock', data?.stock)

                setValue('descriptionEn', data?.description?.en ?? "")
                setValue('descriptionAr', data?.description?.ar ?? "")
                setValue('descriptionTwoEn', data?.descriptionTwo?.en ?? "")
                setValue('descriptionTwoAr', data?.descriptionTwo?.ar ?? "")
                setCustomizable(data?.customizable)
                if (data?.recommendedProducts?.length > 0)
                    setValue(
                        'recommended',
                        data?.recommendedProducts.map(
                            (product: any) => ({
                                value: product?._id,
                                label: product?.name?.en,
                            })
                        )
                    )
                if (data?.boughtTogether?.length > 0)
                    setValue(
                        'boughtTogether',
                        data?.boughtTogether?.map(
                            (product: any) => ({
                                value: product?._id,
                                label: product?.name?.en,
                            })
                        )
                    )
                if (data?.technicalInfo.length > 0) {
                    setValue(
                        'technicalInfo',
                        data?.technicalInfo.map((info: any) => ({
                            titleEn: info?.title?.en,
                            titleAr: info?.title?.ar,
                            contentEn: info?.description?.en,
                            contentAr: info?.description?.ar,
                        }))
                    )
                }
                if (data?.contactSph?.length > 0) setValue('contactSph',
                    data?.contactSph?.map((power: any) => (
                        {
                            value: power?._id,
                            label: power?.name,
                        }
                    ))
                )
                if (data?.contactCyl?.length > 0) setValue('contactCyl',
                    data?.contactCyl?.map((power: any) => (
                        {
                            value: power?._id,
                            label: power?.name,
                        }
                    ))
                )
                if (data?.contactAxis?.length > 0) setValue('contactAxis',
                    data?.contactAxis?.map((power: any) => (
                        {
                            value: power?._id,
                            label: power?.name,
                        }
                    ))
                )
                if (data?.contactSizes.length > 0) {
                    setValue(
                        'contactSizes',
                        data?.contactSizes?.map((size: any) => ({
                            size: {
                                value: size?.size?._id,
                                label: size?.size?.name,
                            },
                            stock: size?.stock,
                            price: size?.price,
                            offerPrice: size?.offerPrice,
                        }))
                    )
                }
                setValue('isActive', {
                    value: data?.isActive,
                    label: data?.isActive ? 'True' : 'False',
                })
                setValue('isNewArrival', {
                    value: data?.isNewArrival,
                    label: data?.isNewArrival ? 'True' : 'False',
                })
                setValue('isVirtualTry', {
                    value: data?.isVirtualTry,
                    label: data?.isVirtualTry ? 'True' : 'False',
                })
                setValue('label', {
                    value: data?.label?._id,
                    label: data?.label?.name?.en,
                })
                setValue('color', {
                    value: data?.color?._id,
                    label: data?.color?.name?.en,
                })
                setThumbnailFiles([data?.thumbnail])
                setLensImageFiles([data?.lensImage])
                setProductFiles(
                    data?.images?.map((image: any) => image)
                )
                setDescriptionFiles(
                    data?.descriptionImages?.map(
                        (image: any) => image
                    )
                )
                setIsShowPercentage(data?.showDiscountPercentage)
                setIsTaxIncluded(data?.isTaxIncluded)
                setValue('supplierSku', data?.supplierSku)
                setValue('upc', data?.upc)
                if (data?.sph) setSphChecked(true)
                if (data?.cyl) setCylChecked(true)
                if (data?.axis) setAxisChecked(true)
                if (data?.multiFocal) setMultiFocalChecked(true)
                if (data?.powerPrice) setValue('powerPrice', data?.powerPrice);

                setValue('metaTitleEn', data?.seoDetails?.title?.en ?? "")
                setValue('metaTitleAr', data?.seoDetails?.title?.ar ?? "")
                setValue('metaDescriptionEn', data?.seoDetails?.description?.en ?? "")
                setValue('metaDescriptionAr', data?.seoDetails?.description?.ar ?? "")
                setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en ?? "")
                setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar ?? "")
                setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en ?? "")
                setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar ?? "")
                setOgImageFile([data?.seoDetails?.ogImage])
            }
        }
    }, [res])

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const {
        handleSubmit,
        control,
        setValue,
        watch,
        formState: { errors },
    } = useForm<any>()

    const watchCats = watch('category')
    const watchSubCats = watch('subCategory')

    useEffect(() => {
        const pop = async () => {
            if (watchSubCats?.length > 0) {
                const data: any = []
                for (let cat of watchSubCats) {
                    if (cat?.value in ref.current) {
                        data.push(...ref.current[cat?.value])
                    } else {
                        const res = await api.get(endpoints.getChildCategories + cat?.value)
                        res.data?.result?.forEach((item: any) => {
                            data.push({ label: item?.name?.en, value: item?._id })
                        })
                        ref.current[cat?.value] = data
                    }
                }
                setSubChildCats(data)
            } else setSubChildCats([])
        }
        pop()
    }, [watchSubCats])

    useEffect(() => {
        const pop = async () => {
            if (watchCats?.length > 0) {
                const data: any = []
                for (let cat of watchCats) {
                    if (cat?.value in ref.current) {
                        data.push(...ref.current[cat?.value])
                    } else {
                        const res = await api.get(endpoints.getChildCategories + cat?.value)
                        res.data?.result?.forEach((item: any) => {
                            data.push({ label: item?.name?.en, value: item?._id })
                        })
                        ref.current[cat?.value] = data
                    }
                }
                setSubCats(data)
            } else setSubCats([])
        }
        pop()
    }, [watchCats])

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'technicalInfo',
    })

    const {
        fields: sizeFields,
        append: sizeAppend,
        remove: sizeRemove,
    } = useFieldArray({
        control,
        name: 'contactSizes',
    })

    const onSubmit = (value: any, e: any) => {
        console.log('form values ::', value)
        let hasError = false

        if (thumbnailFiles.length == 0) {
            setThumbnailError('Thumbnail image is required')
            hasError = true
        } else setThumbnailError(null)

        if (productFiles?.length == 0) {
            setProductError('Product image is required')
            hasError = true
        } else setProductError(null)

        if(lensImageFiles.length == 0) {
            setLensImageError('Lens image is required')
            hasError = true
        } else setLensImageError(null)

        if (descriptionFiles?.length == 0) {
            setDescriptionError('Description image is required')
            hasError = true
        } else setDescriptionError(null)

        console.log('hasError ::', hasError)
        if (hasError) return

        const cats: any = []
        if (value?.subCategory?.length > 0) {
            cats.push(...value?.subCategory?.map((category: any) => category?.value))
        }
        if (value?.subChildCategory?.length > 0) {
            cats.push(...value?.subChildCategory?.map((category: any) => category?.value))
        }

        let payload: any = {
            name: {
                en: value.nameEn,
                ar: value.nameAr,
            },
            brand: value.brand.value,
            weight: value.weight,
            position: value.position,
            slug: value.slug,
            gender: value?.gender?.value,
            plans: value?.plans?.length > 0 ? value?.plans?.map((plan: any) => plan.value) : [],
            category: value?.category?.map((category: any) => category.value),
            subCategory: cats,
            // ageGroup: value?.ageGroup?.length > 0 && value?.ageGroup?.map((ageGroup: any) => ageGroup?.value) || [],
            recommendedProducts: value?.recommended?.length > 0 && value?.recommended?.map(
                (product: any) => product.value
            ) || [],
            boughtTogether: value?.boughtTogether?.length > 0 && value?.boughtTogether?.map(
                (product: any) => product.value
            ) || [],
            sku: value.sku,
            price: {
                aed: value.priceAed,
            },
            offerPrice: {
                aed: value.offerPriceAed,
            },
            stock: value.stock,
            description: {
                en: value.descriptionEn,
                ar: value.descriptionAr,
            },
            descriptionTwo: {
                en: value.descriptionTwoEn,
                ar: value.descriptionTwoAr,
            },
            isCashbackEnabled: value.isCashbackEnabled?.value,
            cashbackPercentage: value.cashbackPercentage,
            productType: 'contactLens',
            isNewArrival: value.isNewArrival.value,
            label: value?.label?.value,
            technicalInfo: value.technicalInfo.map((technicalInfo: any) => ({
                title: {
                    en: technicalInfo.titleEn,
                    ar: technicalInfo.titleAr
                },
                description: {
                    en: technicalInfo.contentEn,
                    ar: technicalInfo.contentAr
                },
            })),
            color: value?.color?.value,
            thumbnail: thumbnailFiles[0],
            lensImage: lensImageFiles[0],
            images: productFiles,
            video: videoUrl,
            descriptionImages: descriptionFiles,
            sph: sphChecked,
            cyl: cylChecked,
            axis: axisChecked,
            isTaxIncluded: isTaxIncluded,
            multiFocal: multiFocalChecked,
            showDiscountPercentage: isShowPercentage,
            powerPrice: value?.powerPrice ? value?.powerPrice : '',
            supplierSku: value.supplierSku,
            upc: value.upc,
            customizable,
            contactSph: value?.contactSph?.length > 0 && value?.contactSph?.map(
                (power: any) => power.value
            ) || [],
            contactCyl: value?.contactCyl?.length > 0 && value?.contactCyl?.map(
                (power: any) => power.value
            ) || [],
            contactAxis: value?.contactAxis?.length > 0 && value?.contactAxis?.map(
                (power: any) => power.value
            ) || [],
            seoDetails: {
                title: {
                    en: value.metaTitleEn,
                    ar: value.metaTitleAr,
                },
                description: {
                    en: value.metaDescriptionEn,
                    ar: value.metaDescriptionAr,
                },
                keywords: {
                    en: value.metaKeywordsEn,
                    ar: value.metaKeywordsAr,
                },
                canonical: {
                    en: value.metaCanonicalUrl,
                    ar: value.metaCanonicalUrlAr,
                },
                ogImage: ogImageFile[0]
            }
        }

        if (customizable) {
            console.log(value?.contactSizes)
            payload.contactSizes = value?.contactSizes?.map((size: any) => ({
                size: size.size?.value,
                stock: size?.stock,
                price: size?.price,
                offerPrice: size?.offerPrice,
            }))
        }
        console.log(payload.contactSizes)

        if (params.id) {
            payload.isActive = value.isActive.value
            payload.refid = params.id

            api.post(endpoints.updateContactlenses, payload).then((res) => {
                if (res.status == 200) {
                    if (res?.data?.errorCode == 0) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        // navigate('/contact-lenses')
                        if (Boolean(e?.target?.dataset?.close)) {
                            navigate('/catalog/products')
                        }
                    }
                } else {
                    toast.push(
                        <Notification
                            type="warning"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
            }).catch((err) => {
                console.log(err)
                toast.push(
                    <Notification
                        type="warning"
                        title={err?.response?.data?.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )

                if (err?.response?.data?.message == "Slug already exists") {
                    control.setError("slug", { message: "Slug already exist" }, { shouldFocus: true })
                }
            })
        } else {
            api.post(endpoints.createContactlenses, payload)
                .then((res) => {
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            // navigate('/contact-lenses')
                            if (Boolean(e?.target?.dataset?.close)) {
                                navigate('/catalog/products')
                            }
                        }
                    } else {
                        toast.push(
                            <Notification
                                type="warning"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    console.log(err)
                })
        }
    }

    return (
        <form>
            <h5>Name</h5>
            <div className="grid grid-cols-2 gap-4 mt-2">
                <FormItem label="English">
                    <Controller
                        name="nameEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.nameEn
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                                onChange={(e) => {
                                    onNameChange(e)
                                    field.onChange(e)
                                }}
                            />
                        )}
                    />
                    {errors.nameEn && (
                        <small className="text-red-600 py-3">
                            {errors.nameEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="nameAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.nameAr && (
                        <small className="text-red-600 py-3">
                            {errors.nameAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Brand">
                    <Controller
                        name="brand"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select options={brandOptions} {...field} />
                        )}
                    />
                    {errors.brand && (
                        <small className="text-red-600 py-3">
                            {errors.brand.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Category">
                    <Controller
                        name="category"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select
                                isMulti
                                options={categoryOptions}
                                {...field}
                            />
                        )}
                    />
                    {errors.category && (
                        <small className="text-red-600 py-3">
                            {errors.category.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            {subCats && subCats.length > 0 && <div className="grid grid-cols-2 gap-4">
                <FormItem label="Sub Category">
                    <Controller
                        name="subCategory"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select
                                isMulti
                                options={subCats}
                                {...field}
                            />
                        )}
                    />
                    {errors.subCategory && (
                        <small className="text-red-600 py-3">
                            {errors.subCategory.message as string}
                        </small>
                    )}
                </FormItem>

                {subChildCats && subChildCats?.length > 0 && <FormItem label="Third Category">
                    <Controller
                        name="subChildCategory"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select
                                isMulti
                                options={subChildCats}
                                {...field}
                            />
                        )}
                    />
                    {errors.subChildCategory && (
                        <small className="text-red-600 py-3">
                            {errors.subChildCategory.message as string}
                        </small>
                    )}
                </FormItem>}
            </div>}

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Position">
                    <Controller
                        name="position"
                        control={control}
                        defaultValue="0"
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input min={0} type="number" {...field} />
                        )}
                    />
                    {errors.position && (
                        <small className="text-red-600 py-3">
                            {errors.position.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Slug">
                    <Controller
                        name="slug"
                        control={control}
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input type="text" {...field} />
                        )}
                    />
                    {errors.slug && (
                        <small className="text-red-600 py-3">
                            {errors.slug.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-2">
                <FormItem label="Cashback">
                    <Controller
                        name="isCashbackEnabled"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select {...field} options={options} />
                        )}
                    />
                </FormItem>
                <FormItem label="Cashback Percentage">
                    <Controller
                        name="cashbackPercentage"
                        control={control}
                        defaultValue="0"
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input min={0} type="number" {...field} />
                        )}
                    />
                    {errors.cashbackPercentage && (
                        <small className="text-red-600 py-3">
                            {errors.cashbackPercentage.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-2">
                <FormItem label="Subscription Plans">
                    <Controller
                        name="plans"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select isMulti options={planOptions} {...field} />
                        )}
                    />
                    {errors.plans && (
                        <small className="text-red-600 py-3">
                            {errors.plans.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-2">
                <FormItem label="Supplier SKU">
                    <Controller
                        name="supplierSku"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input {...field} />
                        )}
                    />
                    {errors.supplierSku && <small className="text-red-600 py-3">{errors.supplierSku.message as string}</small>}
                </FormItem>

                <FormItem label="UPC">
                    <Controller
                        name="upc"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input {...field} />
                        )}
                    />
                    {errors.upc && <small className="text-red-600 py-3">{errors.upc.message as string}</small>}
                </FormItem>
            </div>

            <div>
                <FormItem label="Thumbnail">
                    <Dropzone
                        previews={thumbnailFiles}
                        onFileUrlChange={(e) => setThumbnailFiles([e])}
                        ratio={[500, 240]}
                    />
                    {/* <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={thumbnailFiles}
                        onChange={handleThumbnailUpload}
                    /> */}
                    {thumbnailError && (
                        <small className="text-red-600">{thumbnailError}</small>
                    )}
                </FormItem>
            </div>

            <div>
                <FormItem label="Product Images">
                    <Dropzone
                        multiple
                        previews={productFiles}
                        onFileUrlChange={(e) => {
                            setProductFiles(e)
                            console.log(e)
                        }}
                        ratio={[2560, 1800]}
                    />
                    {/* <Upload
                        draggable
                        multiple
                        accept="image/*"
                        fileList={productFiles}
                        onChange={handleProductUpload}
                    /> */}
                    {productError && (
                        <small className="text-red-600">{productError}</small>
                    )}
                </FormItem>
            </div>

            <div>
                <FormItem label="Lens Image">
                    <Dropzone
                        previews={lensImageFiles}
                        onFileUrlChange={(e) => setLensImageFiles([e])}
                        ratio={[500, 240]}
                    />
                    {/* <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={thumbnailFiles}
                        onChange={handleThumbnailUpload}
                    /> */}
                    {lensImageError && (
                        <small className="text-red-600">{lensImageError}</small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-3 gap-4">
                <FormItem label="SKU">
                    <Controller
                        name="sku"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.sku
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.sku && (
                        <small className="text-red-600 py-3">
                            {errors.sku.message as string}
                        </small>
                    )}
                </FormItem>

                {/* <FormItem label="Age Groups">
                    <Controller
                        name="ageGroup"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select isMulti options={ageOptions} {...field} />
                        )}
                    />
                </FormItem> */}

                <FormItem label="Gender">
                    <Controller
                        name="gender"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select options={genderOptions} {...field} />
                        )}
                    />
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Colour">
                    <Controller
                        name="color"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select options={colorOptions} {...field} />
                        )}
                    />
                    {errors.color && <small className="text-red-600 py-3">{errors.color.message as string}</small>}
                </FormItem>
            </div>

            {(params.type === 'Add-Product' ||
                params.type === 'Edit-Product') && (
                    <div className="my-4 font-semibold">
                        <Checkbox checked={customizable} onChange={onCheck}>
                            Customizable
                        </Checkbox>
                    </div>
                )}

            {/* <div className="grid grid-cols-3 gap-4">
                <FormItem label="Stock">
                    <Controller
                        name="stock"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input min={0} type="number" {...field} />
                        )}
                    />
                    {errors.stock && <small className="text-red-600 py-3">{errors.stock.message as string}</small>}
                </FormItem>

                <FormItem label="Price">
                    <Controller
                        name="priceAed"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input min={0} type="number" step="any"  {...field} />
                        )}
                    />
                    {errors.priceAed && <small className="text-red-600 py-3">{errors.priceAed.message as string}</small>}
                </FormItem>

                <FormItem label="Offer Price">
                    <Controller
                        name="offerPriceAed"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Input min={0} type="number" step="any" {...field} />
                        )}
                    />
                </FormItem>
            </div> */}

            {customizable && (
                <ul className="mt-6">
                    {sizeFields.map((item, index) => {
                        return (
                            <li
                                key={item.id}
                                className="border-2 rounded-md border-gray-400 mt-2 py-6 px-3 relative"
                            >
                                <div className="grid grid-cols-2 gap-4">
                                    <FormItem label="Size">
                                        <Controller
                                            name={`contactSizes.${index}.size`}
                                            control={control}
                                            defaultValue=""
                                            rules={{
                                                required: 'Field is required',
                                            }}
                                            render={({ field }) => (
                                                <Select
                                                    options={sizeOptions}
                                                    {...field}
                                                />
                                            )}
                                        />
                                        {errors.contactSizes &&
                                            Array.isArray(errors.contactSizes) &&
                                            errors.contactSizes[index] &&
                                            'size' in errors.contactSizes[index] &&
                                            'message' in
                                            (errors.contactSizes[index] as any)
                                                .size && (
                                                <small className="text-red-600 py-3">
                                                    {
                                                        (
                                                            errors.contactSizes[
                                                            index
                                                            ] as any
                                                        ).size.message
                                                    }
                                                </small>
                                            )}
                                    </FormItem>

                                    <FormItem label="Stock">
                                        <Controller
                                            name={`contactSizes.${index}.stock`}
                                            control={control}
                                            defaultValue=""
                                            rules={{
                                                required: 'stock is required',
                                            }}
                                            render={({ field }) => (
                                                <Input
                                                    type="number"
                                                    min={0}
                                                    {...field}
                                                />
                                            )}
                                        />
                                        {errors.contactSizes && (
                                            <small className="text-red-600 py-3">
                                                {errors.contactSizes.message as string}
                                            </small>
                                        )}
                                        {errors.contactSizes &&
                                            Array.isArray(errors.contactSizes) &&
                                            errors.contactSizes[index] &&
                                            'stock' in errors.contactSizes[index] &&
                                            'message' in
                                            (errors.contactSizes[index] as any)
                                                .stock && (
                                                <small className="text-red-600 py-3">
                                                    {
                                                        (
                                                            errors.contactSizes[
                                                            index
                                                            ] as any
                                                        ).stock.message
                                                    }
                                                </small>
                                            )}
                                    </FormItem>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <FormItem label="Price">
                                        <Controller
                                            name={`contactSizes.${index}.price`}
                                            control={control}
                                            defaultValue=""
                                            rules={{
                                                required: 'Field is required',
                                            }}
                                            render={({ field }) => (
                                                <Input
                                                    type="number"
                                                    min={0}
                                                    step="any"
                                                    {...field}
                                                />
                                            )}
                                        />
                                        {errors.contactSizes &&
                                            Array.isArray(errors.contactSizes) &&
                                            errors.contactSizes[index] &&
                                            'price' in errors.contactSizes[index] &&
                                            'message' in
                                            (errors.contactSizes[index] as any)
                                                .price && (
                                                <small className="text-red-600 py-3">
                                                    {
                                                        (
                                                            errors.contactSizes[
                                                            index
                                                            ] as any
                                                        ).price.message
                                                    }
                                                </small>
                                            )}
                                    </FormItem>

                                    <FormItem label="Offer Price">
                                        <Controller
                                            name={`contactSizes.${index}.offerPrice`}
                                            control={control}
                                            defaultValue=""
                                            render={({ field }) => (
                                                <Input
                                                    type="number"
                                                    min={0}
                                                    step="any"
                                                    {...field}
                                                />
                                            )}
                                        />
                                    </FormItem>
                                </div>

                                <div className="absolute right-0 top-0">
                                    <IoIosCloseCircleOutline
                                        size={30}
                                        color="red"
                                        onClick={() => sizeRemove(index)}
                                    />
                                </div>
                            </li>
                        )
                    })}
                </ul>
            )}

            {customizable && (
                <Button
                    className="my-3"
                    variant="solid"
                    type="button"
                    icon={<AiOutlinePlus />}
                    onClick={() => {
                        sizeAppend({})
                    }}
                >
                    Add More Sizes
                </Button>
            )}

            {!customizable && (
                <div className="grid grid-cols-3 gap-4">
                    <FormItem label="Stock">
                        <Controller
                            name="stock"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input min={0} type="number" {...field} />
                            )}
                        />
                        {errors.stock && (
                            <small className="text-red-600 py-3">
                                {errors.stock.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Price">
                        <Controller
                            name="priceAed"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    min={0}
                                    type="number"
                                    step="any"
                                    {...field}
                                />
                            )}
                        />
                        {errors.priceAed && (
                            <small className="text-red-600 py-3">
                                {errors.priceAed.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Offer Price">
                        <Controller
                            name="offerPriceAed"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Input
                                    min={0}
                                    type="number"
                                    step="any"
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                </div>
            )}

            <div className="my-4 font-semibold">
                <Checkbox checked={isShowPercentage} onChange={onIsShowPercentage}>
                    Show Discount Percentage
                </Checkbox>
            </div>

            <div>
                <FormItem label="Description ( English )">
                    <Controller
                        name="descriptionEn"
                        defaultValue=""
                        control={control}
                        render={({ field }) => (
                            <RichTextEditor {...field} modules={'image'} />
                        )}
                    />
                </FormItem>
            </div>

            <div>
                <FormItem label="Description ( Arabic )">
                    <Controller
                        name="descriptionAr"
                        defaultValue=""
                        control={control}
                        render={({ field }) => (
                            <RichTextEditor {...field} modules={'image'} />
                        )}
                    />
                </FormItem>
            </div>

            <div>
                <FormItem label="Description two ( English )">
                    <Controller
                        name="descriptionTwoEn"
                        defaultValue=""
                        control={control}
                        render={({ field }) => (
                            <RichTextEditor
                                {...field}
                                modules={'image'}
                            />
                        )}
                    />
                </FormItem>
            </div>

            <div>
                <FormItem label="Description two ( Arabic )">
                    <Controller
                        name="descriptionTwoAr"
                        defaultValue=""
                        control={control}
                        render={({ field }) => (
                            <RichTextEditor
                                {...field}
                                modules={'image'}
                            />
                        )}
                    />
                </FormItem>
            </div>


            <Button
                className="mt-6"
                variant="solid"
                type="button"
                icon={<AiOutlinePlus />}
                onClick={() => {
                    append({})
                }}
            >
                Add Technical Information
            </Button>

            <ul className="mt-6">
                {fields.map((item, index) => {
                    return (
                        <li key={item.id}>
                            <div className="flex  space-x-2">
                                <FormContainer layout="inline">
                                    <FormItem label="Title(English)">
                                        <Controller
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <input
                                                    type="text"
                                                    className={`${errors.technicalInfo
                                                        ? ' input input-md h-11 input-invalid'
                                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                        }`}
                                                    {...field}
                                                />
                                            )}
                                            name={`technicalInfo.${index}.titleEn`}
                                            control={control}
                                        />
                                    </FormItem>

                                    <FormItem label="Content(English)">
                                        <Controller
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <input
                                                    type="text"
                                                    className={`${errors.technicalInfo
                                                        ? ' input input-md h-11 input-invalid'
                                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                        }`}
                                                    {...field}
                                                />
                                            )}
                                            name={`technicalInfo.${index}.contentEn`}
                                            control={control}
                                        />
                                    </FormItem>

                                    <FormItem label="Title(Arabic)">
                                        <Controller
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <input
                                                    dir="rtl"
                                                    type="text"
                                                    className={`${errors.technicalInfo
                                                        ? ' input input-md h-11 input-invalid'
                                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                        }`}
                                                    {...field}
                                                />
                                            )}
                                            name={`technicalInfo.${index}.titleAr`}
                                            control={control}
                                        />
                                    </FormItem>

                                    <FormItem label="Content(Arabic)">
                                        <Controller
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <input
                                                    dir="rtl"
                                                    type="text"
                                                    className={`${errors.technicalInfo
                                                        ? ' input input-md h-11 input-invalid'
                                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                        }`}
                                                    {...field}
                                                />
                                            )}
                                            name={`technicalInfo.${index}.contentAr`}
                                            control={control}
                                        />
                                    </FormItem>
                                </FormContainer>

                                <Button
                                    size="sm"
                                    shape="circle"
                                    type="button"
                                    icon={<AiOutlineMinus />}
                                    onClick={() => remove(index)}
                                ></Button>
                            </div>
                        </li>
                    )
                })}
            </ul>

            <div>
                <FormItem label="Description Images">
                    <Dropzone
                        multiple
                        previews={descriptionFiles}
                        onFileUrlChange={(e) => {
                            setDescriptionFiles(e)
                            console.log(e)
                        }}
                        ratio={[1080, 1060]}
                    />
                    {descriptionError && (
                        <small className="text-red-600">
                            {descriptionError}
                        </small>
                    )}
                </FormItem>
            </div>

            <p className="font-bold font-gray-700">Video Upload</p>
            <div className="flex flex-col items-center justify-center border border-dashed border-gray-400 p-4 mb-4">
                <label
                    htmlFor="upload-button"
                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full cursor-pointer"
                >
                    Select Video
                </label>
                <input
                    type="file"
                    id="upload-button"
                    className="hidden"
                    accept="video/mp4,video/x-m4v,video/*"
                    onChange={handleFileChange}
                />
                {videoPreview && (
                    <div key={videoPreview} className="mt-4">
                        <video
                            width="320"
                            height="240"
                            controls
                            className="rounded-lg shadow-lg"
                        >
                            <source src={videoPreview} type="video/mp4" />
                            Your browser does not support the video tag.
                        </video>
                    </div>
                )}
                {videoFile && (
                    <button
                        type="button"
                        className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-full mt-4"
                        onClick={() => handleVideoUpload()}
                    >
                        Upload
                    </button>
                )}
            </div>

            <div className="grid grid-cols-3 gap-4">
                <FormItem label="Product Weight">
                    <Controller
                        name="weight"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input type="number" {...field} />
                        )}
                    />
                    {errors.weight && (
                        <small className="text-red-600 py-3">
                            {errors.weight.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Recommended Products">
                    <Controller
                        name="recommended"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select
                                isMulti
                                options={productOptions}
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem label="is New Arrival? ">
                    <Controller
                        name="isNewArrival"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select options={options} {...field} />
                        )}
                    />
                    {errors.isNewArrival && (
                        <small className="text-red-600 py-3">
                            {errors.isNewArrival.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-3 gap-4 ">
                <FormItem label="Labels">
                    <Controller
                        name="label"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select
                                options={labels}
                                isSearchable={false}
                                {...field}
                                onChange={(e) => {
                                    if (field?.value?.value === e?.value) {
                                        setValue("label", { value: null })
                                    } else {
                                        field.onChange(e)
                                    }
                                }}
                            />
                        )}
                    />
                </FormItem>

                {params.id && (
                    <FormItem label="is Active ? ">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                        {errors.isActive && (
                            <small className="text-red-600 py-3">
                                {errors.isActive.message as string}
                            </small>
                        )}
                    </FormItem>
                )}
                <FormItem label="Bought Together">
                    <Controller
                        name="boughtTogether"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select
                                isMulti
                                options={productOptions}
                                {...field}
                            />
                        )}
                    />
                </FormItem>
            </div>

            <div className="flex gap-4 mb-2">
                <Checkbox
                    checked={sphChecked}
                    onChange={(checked) => setSphChecked(checked)}
                >
                    SPH
                </Checkbox>
                <Checkbox
                    checked={cylChecked}
                    onChange={(checked) => setCylChecked(checked)}
                >
                    CYL
                </Checkbox>
                <Checkbox
                    checked={axisChecked}
                    onChange={(checked) => setAxisChecked(checked)}
                >
                    AXIS
                </Checkbox>
                <Checkbox
                    checked={multiFocalChecked}
                    onChange={(checked) => setMultiFocalChecked(checked)}
                >
                    Muli-Focal
                </Checkbox>
            </div>

            <div className="grid grid-cols-3 gap-4 mt-4">
                {sphChecked && <FormItem label="SPH Values">
                    <Controller
                        name="contactSph"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select isMulti options={sphValues} {...field} />
                        )}
                    />
                    {/* {errors.frontMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.frontMaterial.message as string}
                        </small>
                    )} */}
                </FormItem>}

                {cylChecked && <FormItem label="CYL Values">
                    <Controller
                        name="contactCyl"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select isMulti options={cylValues} {...field} />
                        )}
                    />
                    {/* {errors.type && (
                        <small className="text-red-600 py-3">
                            {errors.type.message as string}
                        </small>
                    )} */}
                </FormItem>}

                {axisChecked && <FormItem label="AXIS Values">
                    <Controller
                        name="contactAxis"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select isMulti options={axisValues} {...field} />
                        )}
                    />
                    {/* {errors.lensMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.lensMaterial.message as string}
                        </small>
                    )} */}
                </FormItem>}
            </div>

            {
                sphChecked || cylChecked || axisChecked ? (
                    <div className="grid grid-cols-2 gap-4">
                        <FormItem label="Power price">
                            <Controller
                                name="powerPrice"
                                control={control}
                                defaultValue=""
                                render={({ field }) => (
                                    <Input type="number" {...field} />
                                )}
                            />
                        </FormItem>
                    </div>
                ) : null
            }

            <div className="my-4 font-semibold">
                <Checkbox checked={isTaxIncluded} onChange={onIsTaxIncluded}>
                    is Tax Included
                </Checkbox>
            </div>

            <h5 className="mt-10">Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta OG Image">
                    <Dropzone
                        previews={ogImageFile}
                        onFileUrlChange={(e) => setOgImageFile([e])}
                        ratio={[500, 240]}
                    />
                    {/* <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={thumbnailFiles}
                        onChange={handleThumbnailUpload}
                    /> */}
                    {/* {thumbnailError && (
                        <small className="text-red-600">{thumbnailError}</small>
                    )} */}
                </FormItem>

            </div>


            <Button
                className="float-right mt-6 mx-4 [&_*]:pointer-events-none"
                variant="solid"
                type="button"
                data-close={true}
                onClick={handleSubmit(onSubmit)}
                icon={<AiOutlineSave />}
            >
                Save & close
            </Button>
            <Button
                className="float-right mt-6"
                variant="solid"
                type="button"
                data-close={false}
                onClick={handleSubmit(onSubmit)}
                icon={<AiOutlineSave />}
            >
                Save & Continue
            </Button>
        </form>
    )
}
