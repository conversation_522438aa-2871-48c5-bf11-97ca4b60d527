import { Button, FormItem, toast, Select, Upload } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useForm } from "react-hook-form";
import { AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from "react-router-dom";
import Input from '@/components/ui/Input'
import { useEffect, useState } from "react";
import Breadcrumb from "../modals/BreadCrumb";

const imageUrl = import.meta.env.VITE_ASSET_URL

/* eslint-disable */
export default function AddStore() {
    const navigate = useNavigate()
    const params = useParams()
    const [imageFile, setImageFile] = useState<any>([])
    const [imageError, setImageError] = useState<any>(null);

    const breadcrumbItems = [
        { title: 'Providers', url: '/insurance-provider' },
        { title: params?.id ? 'Update Provider' : 'Create Provider', url: '' },
    ]

    const handleImageUpload = (files: any) => {
        setImageFile(files)
    }

    const options: any = [
        { value: "true", label: "True" },
        { value: "false", label: "False" },
    ]

    const {
        handleSubmit,
        setValue,
        control,
        formState: { errors },
    } = useForm<any>();

    useEffect(() => {
        if (params.id)
            api.get(endpoints.providerDetail + params.id).then((res) => {
                if (res?.status == 200) {
                    setValue('nameEn', res.data.result.name.en)
                    setValue('nameAr', res.data.result.name.ar)
                    setImageFile([imageUrl + res.data.result.logo])
                    setValue('isActive', { value: res.data.result.isActive, label: res.data.result.isActive ? 'True' : 'False' })
                }
            }).catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }, [])

    const onSubmit = (value: any) => {
        if (imageFile.length == 0) {
            setImageError('Image is required')
            return;
        }
        setImageError(null)

        const formData = new FormData();
        formData.append('name[en]', value.nameEn)
        formData.append('name[ar]', value.nameAr)
        if (imageFile.length > 0) formData.append('logo', imageFile[0])
        if (params.id) {
            formData.append('isActive', value.isActive.value)
            api.put(endpoints.updateProvider + params.id, formData).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/insurance-provider');
                }
            }).catch((err) => {
                console.log(err);
            })
        }
        else {
            api.post(endpoints.addProvider, formData).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/insurance-provider');
                }
            }).catch((err) => {
                console.log(err);
            })
        };
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-6">{params.id ? 'Edit' : 'Add'} Provider</h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
                <FormItem label="Name English">
                    <Controller
                        name="nameEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${errors.name ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                            />
                        )}
                    />
                    {errors.nameEn && <small className="text-red-600 py-3">{errors.nameEn.message as string}</small>}
                </FormItem>
                <FormItem label="Name Arabic">
                    <Controller
                        name="nameAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                className={`${errors.name ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                            />
                        )}
                    />
                    {errors.nameAr && <small className="text-red-600 py-3">{errors.nameAr.message as string}</small>}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                {params.id &&
                    <FormItem label="isActive? ">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    defaultValue={""}
                                    {...field}
                                    options={options}
                                />
                            )}
                        />
                    </FormItem>
                }
            </div>

            <div>
                <FormItem label="Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={imageFile}
                        onChange={handleImageUpload}
                        ratio={[480, 148]}
                    />
                    {imageError && <small className="text-red-600">{imageError}</small>}
                </FormItem>
            </div>

            <Button className='float-right mt-4' variant="solid" type="submit" icon={<AiOutlineSave />}>
                Save
            </Button>

        </form>
    )
}
