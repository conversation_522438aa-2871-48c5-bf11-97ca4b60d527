// src/Tiptap.tsx
import { use<PERSON><PERSON><PERSON>, EditorContent, useEditorState, ReactNodeViewRenderer } from '@tiptap/react'
import { BubbleMenu } from '@tiptap/react/menus'
import StarterKit from '@tiptap/starter-kit'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import { TextStyle, FontSize, Color, BackgroundColor } from '@tiptap/extension-text-style'
import FileHandler from '@tiptap/extension-file-handler'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import Image from '@tiptap/extension-image'
import { MdBorderColor, MdColorLens } from "react-icons/md";
import {
    FaBold,
    FaUnderline,
    FaItalic,
    FaStrikethrough,
    FaQuoteLeft,
    FaCode,
    FaListOl,
    FaListUl,
    FaSubscript,
    FaSuperscript,
    FaAlignLeft,
    FaAlignRight,
    FaAlignCenter,
    FaAlignJustify,
    FaLink,
    FaFileImage
} from "react-icons/fa";
import { BsTypeH1, BsTypeH2 } from "react-icons/bs";
import { ImEmbed } from "react-icons/im";
import { twi } from 'tw-to-css'
import { Button, Dialog, Input, Select } from '../ui'
import { useCallback, useEffect, useState } from 'react'
import { Dropzone } from './Dropzone'
import { Node } from '@tiptap/core'



const baseUrl = import.meta.env.VITE_ASSET_URL

const MyImage = Image.extend({
    onFocus() {
        console.log('Image focused')
    },
})

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        iframe: {
            setIframe: (options: { src: string }) => ReturnType
        }
    }
}

const Iframe = Node.create({
    name: 'iframe',
    group: 'block',
    atom: true,
    addOptions() {
        return {
            allowFullscreen: true,
            HTMLAttributes: {
                class: 'iframe-wrapper',
            },
        }
    },
    addAttributes: function () {
        return ({
            src: {
                default: null
            },
            frameborder: {
                default: 0
            },
            allowfullscreen: {
                default: this.options.allowFullscreen,
                parseHTML: () => this.options.allowFullscreen,
            }
        })
    },
    parseHTML: () => {
        return [
            {
                tag: 'iframe',
            },
        ]
    },
    renderHTML: function ({ HTMLAttributes }) {
        return ['div', this.options.HTMLAttributes, ['iframe', HTMLAttributes]]
    },
    addCommands: function () {
        return {
            setIframe: (option: { src: string }) => ({ tr, dispatch }: any) => {
                const { selection } = tr;
                const node = this.type.create(option);
                if (dispatch) {
                    tr.replaceRangeWith(selection.from, selection.to, node);
                }
                return true;
            }
        }
    }

})

// define your extension array
const extensions = [
    StarterKit.configure({
        heading: {
            levels: [1, 2, 3, 4, 5, 6],
        },
        codeBlock: {
            HTMLAttributes: {
                style: twi("bg-gray-100 p-2 rounded-md")
            },
        },
        blockquote: {
            HTMLAttributes: {
                style: twi("border-l-4 border-gray-300 pl-4")
            },
        },
        orderedList: {
            HTMLAttributes: {
                style: "list-style: decimal; margin-block-start: 1em; margin-block-end: 1em; padding-inline-start: 40px;"
            },
        },
        bulletList: {
            HTMLAttributes: {
                style: "list-style: disc; margin-block-start: 1em; margin-block-end: 1em; padding-inline-start: 40px;"
            },
        },
    }),
    Subscript,
    Superscript,
    FontSize,
    TextStyle,
    Color,
    BackgroundColor,
    TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
    }),
    Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: 'https',
        protocols: ['http', 'https'],
        HTMLAttributes: {
            target: '_blank',
            rel: 'noopener noreferrer',
            style: twi('text-blue-500 underline')
        },
        isAllowedUri: (url, ctx) => {
            try {
                // construct URL
                const parsedUrl = url.includes(':') ? new URL(url) : new URL(`${ctx.defaultProtocol}://${url}`)

                // use default validation
                if (!ctx.defaultValidate(parsedUrl.href)) {
                    return false
                }

                // disallowed protocols
                const disallowedProtocols = ['ftp', 'file', 'mailto']
                const protocol = parsedUrl.protocol.replace(':', '')

                if (disallowedProtocols.includes(protocol)) {
                    return false
                }

                // only allow protocols specified in ctx.protocols
                const allowedProtocols = ctx.protocols.map(p => (typeof p === 'string' ? p : p.scheme))

                if (!allowedProtocols.includes(protocol)) {
                    return false
                }

                // disallowed domains
                const disallowedDomains = ['example-phishing.com', 'malicious-site.net']
                const domain = parsedUrl.hostname

                if (disallowedDomains.includes(domain)) {
                    return false
                }

                // all checks have passed
                return true
            } catch {
                return false
            }
        },
        shouldAutoLink: url => {
            try {
                // construct URL
                const parsedUrl = url.includes(':') ? new URL(url) : new URL(`https://${url}`)

                // only auto-link if the domain is not in the disallowed list
                const disallowedDomains = ['example-no-autolink.com', 'another-no-autolink.com']
                const domain = parsedUrl.hostname

                return !disallowedDomains.includes(domain)
            } catch {
                return false
            }
        },
    }),
    MyImage.configure({
        HTMLAttributes: {
            style: "width: 100%; height: auto;"
        },
    }),
    Iframe
]

const headingOptions = [
    { value: 1, label: 'Heading 1' },
    { value: 2, label: 'Heading 2' },
    { value: 3, label: 'Heading 3' },
    { value: 4, label: 'Heading 4' },
    { value: 5, label: 'Heading 5' },
    { value: 6, label: 'Heading 6' },
]

const textSizes = [
    { value: '10px', label: 'Small' },
    { value: 'auto', label: 'Medium' },
    { value: '20px', label: 'Large' },
]

const textAlign = [
    { value: 'left', label: <FaAlignLeft /> },
    { value: 'center', label: <FaAlignCenter /> },
    { value: 'right', label: <FaAlignRight /> },
    { value: 'justify', label: <FaAlignJustify /> },
]

const Tiptap = ({ value, onChange, ...props }: any) => {
    const [showLinkModal, setShowLinkModal] = useState(false)
    const [showImageModal, setShowImageModal] = useState(false)
    const [showIframeModal, setShowIframeModal] = useState(false)
    const [image, setImage] = useState<any>([])
    const [linkUrl, setLinkUrl] = useState('')
    const [iframeUrl, setIframeUrl] = useState('')

    const editor = useEditor({
        extensions,
        content: value,
        editorProps: {
            attributes: {
                class:
                    'p-4 focus:outline-none',
            },
        },
        onUpdate: ({ editor }) => {
            const html = editor.getHTML()
            onChange(html)
        },
    })

    useEffect(() => {
        if (!editor || !value) return
        if (value !== editor.getHTML()) {
            editor.commands.setContent(value)
        }
    }, [editor, value])

    const editorState = useEditorState({
        editor,
        selector: ({ editor }) => {
            if (!editor) return null;
            return {
                isBold: editor.isActive('bold') ?? false,
                isItalic: editor.isActive('italic') ?? false,
                isUnderline: editor.isActive('underline') ?? false,
                isStrike: editor.isActive('strike') ?? false,
                isBlockQuote: editor.isActive('blockquote') ?? false,
                isCodeBlock: editor.isActive('codeBlock') ?? false,
                isHeading1: editor.isActive('heading', { level: 1 }) ?? false,
                isHeading2: editor.isActive('heading', { level: 2 }) ?? false,
                isHeading3: editor.isActive('heading', { level: 3 }) ?? false,
                isHeading4: editor.isActive('heading', { level: 4 }) ?? false,
                isHeading5: editor.isActive('heading', { level: 5 }) ?? false,
                isHeading6: editor.isActive('heading', { level: 6 }) ?? false,
                isOrderedList: editor.isActive('orderedList') ?? false,
                isBulletList: editor.isActive('bulletList') ?? false,
                isSubscript: editor.isActive('subscript') ?? false,
                isSuperscript: editor.isActive('superscript') ?? false,
                color: editor.getAttributes('textStyle').color ?? null,
                backgroundColor: editor.getAttributes('textStyle').backgroundColor ?? null,
                isTextAlignLeft: editor.isActive({ textAlign: 'left' }) ?? false,
                isTextAlignCenter: editor.isActive({ textAlign: 'center' }) ?? false,
                isTextAlignRight: editor.isActive({ textAlign: 'right' }) ?? false,
                isTextAlignJustify: editor.isActive({ textAlign: 'justify' }) ?? false,
                isLink: editor.isActive('link') ?? false,
            }
        },
    })

    const onSubmitUrl = useCallback(() => {
        if (linkUrl === null) {
            return
        }

        if (linkUrl === '') {
            setShowLinkModal(false)
            editor.chain().focus().extendMarkRange('link').unsetLink().run()
            return
        }
        setShowLinkModal(false)
        try {
            editor.chain().focus().extendMarkRange('link').setLink({ href: linkUrl }).run()
        } catch (e: any) {
            alert(e.message)
        }
    }, [editor, linkUrl])

    const onSubmitImage = () => {
        editor.chain().insertContentAt(editor.state.selection.anchor, {
            type: 'image',
            attrs: {
                src: baseUrl + image[0],
                // src: "https://d166k8idvyo6r9.cloudfront.net/video/1753412761864-round-shape-frame-500x500.webp",
                alt: 'Image',
                style: 'width: 100%; height: auto;'
            }
        }).focus().run()
        setShowImageModal(false)
    }

    const onSubmitIframe = () => {
        editor.chain().focus().setIframe({ src: iframeUrl }).run()
        console.log('setIframe', iframeUrl)
        setShowIframeModal(false)
    }

    return (
        editor && <>
            <div className="border rounded-md [&_p]:my-2 [&_h1]:my-4 [&_h2]:my-3 [&_h3]:my-2 [&_h4]:my-2 [&_h5]:my-2 [&_h6]:my-2">
                <div className="flex flex-wrap gap-1 p-1 border-b">
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleBold().run()}
                        className={`${editorState?.isBold ? "bg-gray-200" : ""}`}
                    >
                        <FaBold size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleItalic().run()}
                        className={`${editorState?.isItalic ? "bg-gray-200" : ""}`}
                    >
                        <FaItalic size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleUnderline().run()}
                        className={`${editorState?.isUnderline ? "bg-gray-200" : ""}`}
                    >
                        <FaUnderline size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleStrike().run()}
                        className={`${editorState?.isStrike ? "bg-gray-200" : ""}`}
                    >
                        <FaStrikethrough size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleBlockquote().run()}
                        className={`${editorState?.isBlockQuote ? "bg-gray-200" : ""}`}
                    >
                        <FaQuoteLeft size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleCodeBlock().run()}
                        className={`${editorState?.isCodeBlock ? "bg-gray-200" : ""}`}
                    >
                        <FaCode size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                        className={`${editorState?.isHeading1 ? "bg-gray-200" : ""}`}
                    >
                        <BsTypeH1 size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                        className={`${editorState?.isHeading2 ? "bg-gray-200" : ""}`}
                    >
                        <BsTypeH2 size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleOrderedList().run()}
                        className={`${editorState?.isOrderedList ? "bg-gray-200" : ""}`}
                    >
                        <FaListOl size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleBulletList().run()}
                        className={`${editorState?.isBulletList ? "bg-gray-200" : ""}`}
                    >
                        <FaListUl size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleSubscript().run()}
                        className={`${editorState?.isSubscript ? "bg-gray-200" : ""}`}
                    >
                        <FaSubscript size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => editor.chain().focus().toggleSuperscript().run()}
                        className={`${editorState?.isSuperscript ? "bg-gray-200" : ""}`}
                    >
                        <FaSuperscript size={14} />
                    </MenuButton>
                    <Select
                        size='sm'
                        placeholder="Heading"
                        options={headingOptions}
                        value={
                            editorState?.isHeading1 ? headingOptions[0] :
                                editorState?.isHeading2 ? headingOptions[1] :
                                    editorState?.isHeading3 ? headingOptions[2] :
                                        editorState?.isHeading4 ? headingOptions[3] :
                                            editorState?.isHeading5 ? headingOptions[4] :
                                                editorState?.isHeading6 ? headingOptions[5] : null
                        }
                        onChange={(option: any) => editor.chain().focus().toggleHeading({ level: option.value }).run()}
                    />
                    <Select
                        size='sm'
                        placeholder="Text"
                        options={textSizes}
                        onChange={(option: any) => editor.chain().focus().setFontSize(option.value).run()}
                    />
                    <div className="relative p-1 my-auto">
                        <label htmlFor="color" title='Color' className='cursor-pointer'>
                            <MdBorderColor size={20} color={editorState?.color} />
                        </label>
                        <Input
                            type="color"
                            id='color'
                            className='sr-only'
                            size='sm'
                            onChange={(e) => editor.chain().focus().setColor(e.target.value).run()}
                            value={editorState?.color}
                        />
                    </div>
                    <div className="relative p-1 my-auto">
                        <label htmlFor="bg-color" title='Background Color' className='cursor-pointer'>
                            <MdColorLens size={20} color={editorState?.backgroundColor} />
                        </label>
                        <Input
                            type="color"
                            id='bg-color'
                            className='sr-only'
                            size='sm'
                            value={editorState?.backgroundColor}
                            onChange={(e) => editor.chain().focus().setBackgroundColor(e.target.value).run()}
                        />
                    </div>
                    <Select
                        isSearchable={false}
                        size='sm'
                        // placeholder="Align"
                        defaultValue={textAlign[0]}
                        options={textAlign}
                        value={
                            editorState?.isTextAlignLeft ? textAlign[0] :
                                editorState?.isTextAlignCenter ? textAlign[1] :
                                    editorState?.isTextAlignRight ? textAlign[2] :
                                        editorState?.isTextAlignJustify ? textAlign[3] : textAlign[0]
                        }
                        onChange={(option: any) => editor.chain().focus().toggleTextAlign(option.value).run()}
                    />
                    <MenuButton
                        onClick={() => setShowLinkModal(true)}
                        className={`${editorState?.isLink ? "bg-gray-200" : ""}`}
                    >
                        <FaLink size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => setShowImageModal(true)}
                    // onClick={onSubmitImage}
                    // className={`${editorState?.isLink ? "bg-gray-200" : ""}`}
                    >
                        <FaFileImage size={14} />
                    </MenuButton>
                    <MenuButton
                        onClick={() => setShowIframeModal(true)}
                    // onClick={onSubmitImage}
                    // className={`${editorState?.isLink ? "bg-gray-200" : ""}`}
                    >
                        <ImEmbed size={14} />
                    </MenuButton>
                </div>
                <EditorContent {...props} className='min-h-[100px] max-h-80 overflow-auto' editor={editor} />
            </div>
            {/* <FloatingMenu editor={editor}>This is the floating menu</FloatingMenu> */}
            {/* <BubbleMenu options={{ placement: 'bottom', offset: 8 }} editor={editor}>

            </BubbleMenu> */}
            <Dialog isOpen={showLinkModal} onClose={() => setShowLinkModal(false)}>
                <div className="flex flex-col gap-4 mt-4">
                    <Input
                        type="text"
                        placeholder="Link"
                        value={linkUrl}
                        onChange={(e) => setLinkUrl(e.target.value)}

                    />
                    <div className="text-right">
                        <Button
                            className="ltr:mr-2 rtl:ml-2 border-black"
                            onClick={() => setShowLinkModal(false)}
                        >
                            Cancel
                        </Button>
                        <Button variant="solid" onClick={onSubmitUrl}>
                            Save
                        </Button>
                    </div>
                </div>
            </Dialog>
            <Dialog isOpen={showImageModal} onClose={() => setShowImageModal(false)}>
                <div className="flex flex-col gap-4 mt-4">
                    <Dropzone
                        previews={image}
                        onFileUrlChange={(e: any) => setImage([e])}
                        ratio={[500, 140]}
                    />
                    <div className="text-right">
                        <Button
                            className="ltr:mr-2 rtl:ml-2 border-black"
                            onClick={() => setShowImageModal(false)}
                        >
                            Cancel
                        </Button>
                        <Button disabled={!image.length} variant="solid" onClick={onSubmitImage}>
                            Save
                        </Button>
                    </div>
                </div>
            </Dialog>
            <Dialog isOpen={showIframeModal} onClose={() => setShowIframeModal(false)}>
                <div className="flex flex-col gap-4 mt-4">
                    <Input
                        type="text"
                        placeholder="Iframe URL"
                        value={iframeUrl}
                        onChange={(e) => setIframeUrl(e.target.value)}

                    />
                    <div className="text-right">
                        <Button
                            className="ltr:mr-2 rtl:ml-2 border-black"
                            onClick={() => setShowIframeModal(false)}
                        >
                            Cancel
                        </Button>
                        <Button variant="solid" onClick={onSubmitIframe}>
                            Save
                        </Button>
                    </div>
                </div>
            </Dialog>

        </>
    )
}

function MenuButton({ children, className, ...props }: any) {
    return (
        <button type='button' className={`hover:bg-slate-100 p-2 rounded ${className}`} {...props}>
            {children}
        </button>
    )
}

export default Tiptap