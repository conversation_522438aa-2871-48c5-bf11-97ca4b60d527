import { forwardRef, useRef, useState, useCallback, useEffect } from 'react'
import classNames from 'classnames'
import { useConfig } from '../ConfigProvider'
import cloneDeep from 'lodash/cloneDeep'
import FileItem from './FileItem'
import Button from '../Button/Button'
import CloseButton from '../CloseButton'
import Notification from '../Notification/Notification'
import toast from '../toast/toast'
import type { CommonProps } from '../@types/common'
import type { ReactNode, ChangeEvent, MouseEvent } from 'react'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { CSS } from "@dnd-kit/utilities";

import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragOverlay,
} from '@dnd-kit/core';

import {
    arrayMove,
    rectSortingStrategy,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
} from '@dnd-kit/sortable';

export interface UploadProps extends CommonProps {
    accept?: string
    beforeUpload?: (file: FileList | null, fileList: File[]) => boolean | string
    disabled?: boolean
    draggable?: boolean
    fileList?: any
    multiple?: boolean
    onChange?: (file: File[], fileList: File[]) => void
    onFileRemove?: (file: File[]) => void
    showList?: boolean
    tip?: string | ReactNode
    uploadLimit?: number
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    field?: any
    ratio?: [number, number]
}

const filesToArray = (files: File[]) =>
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Object.keys(files).map((key) => files[key as any])

const Upload = forwardRef<HTMLDivElement, UploadProps>((props, ref) => {
    const {
        accept,
        beforeUpload,
        disabled = false,
        draggable = false,
        fileList = [],
        multiple,
        onChange,
        onFileRemove,
        showList = true,
        tip,
        uploadLimit,
        children,
        className,
        field,
        ratio = [],
        ...rest
    } = props


    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 5
            }
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const fileInputField = useRef<HTMLInputElement>(null)
    const [files, setFiles] = useState(fileList)
    const [dragOver, setDragOver] = useState(false)

    const { themeColor, primaryColorLevel } = useConfig()

    useEffect(() => {
        if (JSON.stringify(files) !== JSON.stringify(fileList)) {
            setFiles(fileList)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fileList])

    const triggerMessage = (msg: string | ReactNode = '') => {
        toast.push(
            <Notification type="danger" duration={2000}>
                {msg || 'Upload Failed!'}
            </Notification>,
            {
                placement: 'top-center',
            }
        )
    }

    const pushFile = (newFiles: FileList | null, file: File[]) => {
        if (newFiles) {
            for (const f of newFiles) {
                file.push(f)
            }
        }

        return file
    }

    const addNewFiles = (newFiles: FileList | null) => {
        let file = cloneDeep(files)
        if (typeof uploadLimit === 'number' && uploadLimit !== 0) {
            if (Object.keys(file).length >= uploadLimit) {
                if (uploadLimit === 1) {
                    file.shift()
                    file = pushFile(newFiles, file)
                }

                return filesToArray({ ...file })
            }
        }
        file = pushFile(newFiles, file)
        return filesToArray({ ...file })
    }

    const onNewFileUpload = (e: ChangeEvent<HTMLInputElement>) => {
        const { files: newFiles } = e.target;
        let result: boolean | string = true;
        if (newFiles) {
            const file = newFiles[0];
            const maxFileSize = 4 * 1024 * 1024;
            const supportedTypes = accept ? ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/gif', ...accept?.split(" ") as string[]] : ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/gif'];
            if (file.size > maxFileSize) {
                triggerMessage("File size exceeds the maximum limit of 4MB.");
                e.target.value = ''; // Clear the input value to reset the state
                return;
            } else if (!supportedTypes.includes(file.type)) {
                triggerMessage("File type not supported. Only JPEG, PNG and JPG files are allowed.");
                e.target.value = ''; // Clear the input value to reset the state
                return;
            }
        }

        if (beforeUpload) {
            result = beforeUpload(newFiles, files);

            if (result === false) {
                triggerMessage();
                e.target.value = ''; // Clear the input value to reset the state
                return;
            }

            if (typeof result === 'string' && result.length > 0) {
                triggerMessage(result);
                e.target.value = ''; // Clear the input value to reset the state
                return;
            }
        }

        if (result) {
            const updatedFiles = addNewFiles(newFiles);
            setFiles(updatedFiles);
            onChange?.(updatedFiles, files);
        }
    };

    // const onNewFileUpload = (e: ChangeEvent<HTMLInputElement>) => {
    //     const { files: newFiles } = e.target
    //     let result: boolean | string = true

    //     if (beforeUpload) {
    //         result = beforeUpload(newFiles, files)

    //         if (result === false) {
    //             triggerMessage()
    //             return
    //         }

    //         if (typeof result === 'string' && result.length > 0) {
    //             triggerMessage(result)
    //             return
    //         }
    //     }

    //     if (result) {
    //         const updatedFiles = addNewFiles(newFiles)
    //         setFiles(updatedFiles)
    //         onChange?.(updatedFiles, files)
    //     }
    // }

    const removeFile = (fileIndex: number) => {
        // console.log("uploads" + files[fileIndex].split("uploads")[1])
        // api.delete(endpoints.mediaDelete, { data: { filePath: "uploads" + files[fileIndex].split("uploads")[1] } }).then((res) => {
        //     if (res.status == 200) {
        const deletedFileList = files.filter((_: any, index: number) => index !== fileIndex)
        setFiles(deletedFileList)
        onFileRemove?.(deletedFileList)
        //     }
        // }).catch((error) => {
        //     console.error('Error Deleting data: ', error)
        //     toast.push(
        //         <Notification type="danger" duration={2000}>
        //             {error || 'Error Deleting data'}
        //         </Notification>,
        //         {
        //             placement: 'top-center',
        //         }
        //     )
        // })
    }

    const triggerUpload = (e: MouseEvent<HTMLDivElement>) => {
        if (!disabled) {
            fileInputField.current?.click()
        }
        e.stopPropagation()
    }

    const renderChildren = () => {
        if (!draggable && !children) {
            return (
                <Button disabled={disabled} onClick={(e) => e.preventDefault()}>
                    Upload
                </Button>
            )
        }

        if (draggable && !children) {
            return (
                // <span>Choose a file or drag and drop here</span>
                <div className="text-center">
                    <p className="font-semibold">
                        <span className="text-gray-800 dark:text-white">
                            Drop your image here, or{' '}
                        </span>
                        <span className="text-blue-500">browse</span>
                    </p>
                    <p className="mt-1 opacity-80 text-gray-800 dark:text-white pointer-events-none">
                        Support: jpeg, png, webp
                    </p>
                    <p className="mt-1 opacity-80 text-gray-800 dark:text-white pointer-events-none">
                        Max file size: 4 MB
                    </p>
                    <p>Preferred size {ratio[0]}*{ratio[1]} px</p>
                </div>
            )
        }

        return children
    }

    const handleDragLeave = useCallback(() => {
        if (draggable) {
            setDragOver(false)
        }
    }, [draggable])

    const handleDragOver = useCallback((e: any) => {
        e.preventDefault()
        if (draggable && !disabled) {
            setDragOver(true)
        }
    }, [draggable, disabled])

    const handleDrop = useCallback((e: any) => {
        // e.nativeEvent.preventDefault()
        if (draggable) {
            setDragOver(false)
        }
    }, [draggable])

    const draggableProp = {
        onDragLeave: handleDragLeave,
        onDragOver: handleDragOver,
        onDrop: handleDrop,
    }

    const draggableEventFeedbackClass = `border-${themeColor}-${primaryColorLevel}`

    const uploadClass = classNames(
        'upload',
        draggable && `upload-draggable`,
        draggable && !disabled && `hover:${draggableEventFeedbackClass}`,
        draggable && disabled && 'disabled',
        dragOver && draggableEventFeedbackClass,
        className
    )

    const uploadInputClass = classNames(
        'upload-input',
        draggable && `draggable`
    )
 
    const handleDragEnd = useCallback((event: any) => {
        const { active, over }: any = event;
        if (active?.id !== over?.id) {
            const oldIndex = files.findIndex(((item: any) => (item?.name ?? item) == active.id));
            const newIndex = files.findIndex(((item: any) => (item?.name ?? item) == over.id));
            let newCats: any = arrayMove(files, oldIndex, newIndex);
            console.log(newCats)
            onFileRemove?.([...newCats]);
            setFiles([...newCats]);
        }
    }, [files]);

    return (
        <>
            <div
                ref={ref}
                className={uploadClass}
                {...(draggable ? draggableProp : { onClick: triggerUpload })}
                {...rest}
            >
                <input
                    ref={fileInputField}
                    className={uploadInputClass}
                    type="file"
                    disabled={disabled}
                    multiple={multiple}
                    accept={accept}
                    title=""
                    value=""
                    onChange={onNewFileUpload}
                    {...field}
                    {...rest}
                ></input>
                {renderChildren()}
            </div>
            {tip}
            {showList && (
                <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                // onDragStart={handleDragStart}
                >
                    {/* <div className="upload-file-list">
                        {files.map((file: any, index: any) => {
                            return (
                                <FileItem key={index} file={file}>
                                    <CloseButton
                                        className="upload-file-remove"
                                        onClick={() => removeFile(index)}
                                    />
                                </FileItem>
                            )
                        })}
                    </div> */}

                    <SortableContext
                        items={files.map((item: any) => item?.name ?? item)}
                        strategy={rectSortingStrategy}
                    >
                        <div className="upload-file-list">
                            {files.map((file: any, index: number) =>
                                <CategoryDrag key={file?.name ?? file} id={file?.name ?? file}>
                                    <FileItem file={file} key={file?.name ?? file}>
                                        <CloseButton
                                            className="upload-file-remove"
                                            onClick={() => removeFile(index)}
                                        />
                                    </FileItem>
                                </CategoryDrag>
                            )}
                        </div>
                    </SortableContext>

                </DndContext>
            )}
        </>
    )
})

Upload.displayName = 'Upload'

export default Upload

function CategoryDrag(props: any) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging
    } = useSortable({ id: props.id });
    const style = {
        transform: CSS.Transform.toString(transform),
        transition
    };

    return (
        <div
            style={style}
            className={isDragging ? "opacity-50" : ""}
            ref={setNodeRef} {...attributes} {...listeners}
        >
            {props.children}
        </div>

    )
}
