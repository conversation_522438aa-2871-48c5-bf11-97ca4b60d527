/* eslint-disable */
import {
    <PERSON><PERSON>,
    <PERSON>,
    Dialog,
    FormItem,
    Select,
    Tag,
    toast,
} from '@/components/ui'
import Breadcrumb from '../modals/BreadCrumb'
import { BsBoxSeam } from 'react-icons/bs'
import { Link, useParams } from 'react-router-dom'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { useEffect, useMemo, useState } from 'react'
import { MdOutlineLocalShipping } from 'react-icons/md'
import Notification from '@/components/ui/Notification'
import DeleteModal from '../modals/DeleteModal'
import { CiEdit } from 'react-icons/ci'
import ManageAddress from '../customers/ManageAddress'
import LensDetailsModal from './LensDetails'
import ContactLensDetails from './ContactLensDetail'

export default function ManageOrders() {
    const imageBase = import.meta.env.VITE_ASSET_URL
    const params = useParams()
    const [data, setData] = useState<any>()
    const [orderStatus, setOrderStatus] = useState<any>()
    const [paymentStatus, setPaymentStatus] = useState<any>()
    const [cancelModalOpen, setCancelModalOpen] = useState(false)
    const [addressModalOpen, setAddressModalOpen] = useState(false)
    const [lensModalOpen, setLensModalOpen] = useState(false)
    const [address, setAddress] = useState<any>()
    const [updatedOrderStatus, setUpdatedOrderStatus] = useState<any>()
    const [updatedPaymentStatus, setUpdatedPaymentStatus] = useState<any>()
    const [selectedLensDetails, setSelectedLensDetails] = useState(null)
    const [contactLensModalOpen, setContactLensModalOpen] = useState(false)
    const [selectedContactLensDetails, setSelectedContactLensDetails] =
        useState(null)

    const statusesWhereCancelNotAllowed = [
        'CANCELLED',
        // 'SHIPPED',
        // 'SHIPPED VIA ECO',
        // 'SHIPPED VIA INHOUSE',
        // 'OUT FOR DELIVERY',
        'DELIVERED',
        'RETURNED',
    ]

    const toggleLensModal = (lensDetails: any) => {
        setSelectedLensDetails(lensDetails)
        setLensModalOpen(!lensModalOpen)
    }

    const toggleContactLensModal = (contactLensDetails: any) => {
        console.log("contactLensDetails ::", contactLensDetails)
        setSelectedContactLensDetails(contactLensDetails)
        setContactLensModalOpen(!contactLensModalOpen)
    }

    const openAddressModal = (refid: any) => {
        api.post(endpoints.addressDetail, { refid: refid })
            .then((res) => {
                if (res.status == 200) {
                    setAddress(res.data.result)
                    setAddressModalOpen(true)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const closeAddressModal = () => {
        setAddressModalOpen(false)
        getOrderDetails()
    }

    const openCancelModal = () => {
        setCancelModalOpen(true)
    }

    const closeCancelModal = () => {
        setCancelModalOpen(false)
    }

    const confirmCancel = () => {
        api.put(endpoints.updateOrder + params.id, { orderStatus: 'CANCELLED' })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    closeCancelModal()
                    getOrderDetails()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                closeCancelModal()
            })
    }

    const breadCrumbItems = [
        { title: 'Orders', url: '/orders' },
        { title: 'Manage Orders', url: '' },
    ]

    //'READY TO SHIP', 'SHIPPED VIA ECO', 'SHIPPED VIA INHOUSE',

    const baseOrderStatusOptions = [
        { value: 'PLACED', label: 'PLACED' },
        { value: 'CONFIRMED', label: 'CONFIRMED' },
        { value: 'READY TO SHIP', label: 'READY TO SHIP' },
        { value: 'SHIPPED VIA ECO', label: 'SHIPPED VIA ECO' },
        { value: 'SHIPPED VIA INHOUSE', label: 'SHIPPED VIA INHOUSE' },
        // { value: 'OUT FOR DELIVERY', label: 'OUT FOR DELIVERY' },
        { value: 'DELIVERED', label: 'DELIVERED' },
        // { value: 'RETURNED', label: 'RETURNED' },
    ]

    const orderStatusOptions = useMemo(() => {
        if (!orderStatus) {
            return baseOrderStatusOptions
        }
        let currentIndex = baseOrderStatusOptions.findIndex(
            (option) => option.value === orderStatus.value
        )
        if (orderStatus.value === 'SHIPPED VIA ECO') currentIndex = currentIndex + 2

        return baseOrderStatusOptions.slice(currentIndex)
    }, [orderStatus])

    const paymentStatusOptions = [
        { value: 'PENDING', label: 'PENDING' },
        { value: 'PAID', label: 'PAID' },
    ]

    const handleOrderStatus = (value: any, type: any) => {
        console.log('value:', value, 'type :', type)
        if (type === 'orderStatus') {
            setUpdatedOrderStatus({ value: value.value, label: value.label })
        } else {
            setUpdatedPaymentStatus({ value: value.value, label: value.label })
        }
    }

    const saveOrderStatus = () => {
        let payload: any = {}
        if (updatedOrderStatus) {
            payload.orderStatus = updatedOrderStatus.value
        }
        if (updatedPaymentStatus) {
            payload.paymentStatus = updatedPaymentStatus.value
        }

        if (Object.keys(payload).length === 0) {
            // No changes made, return
            return
        }

        console.log('payload:', payload)

        api.put(endpoints.updateOrder + params.id, payload)
            .then((res) => {
                if (res.status === 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getOrderDetails()
                    setUpdatedOrderStatus(null)
                    setUpdatedPaymentStatus(null)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                setUpdatedOrderStatus(null)
                setUpdatedPaymentStatus(null)
            })
    }

    // const handleOrderStatus = (value:any, type: string) => {
    //     let payload: any

    //     if (type == 'orderStatus') payload = { orderStatus: value.value }
    //     else payload = { paymentStatus: value.value }

    //     api.put(endpoints.updateOrder + params.id, payload)
    //         .then((res) => {
    //             if (res.status == 200) {
    //                 toast.push(
    //                     <Notification
    //                         type="success"
    //                         title={res.data.message}
    //                     />,
    //                     {
    //                         placement: 'top-center',
    //                     }
    //                 )
    //                 getOrderDetails()
    //             }
    //         })
    //         .catch((err) => {
    //             toast.push(
    //                 <Notification
    //                     type="warning"
    //                     title={err.response.data.message}
    //                 />,
    //                 {
    //                     placement: 'top-center',
    //                 }
    //             )
    //         })
    // }

    const getOrderDetails = () => {
        api.get(endpoints.orderDetail + params.id)
            .then((res) => {
                if (res.status == 200) {
                    setData(res.data.result)
                    setOrderStatus(
                        orderStatusOptions.find(
                            (option: any) =>
                                option.value == res.data.result.orderStatus
                        )
                    )
                    setPaymentStatus(
                        paymentStatusOptions.find(
                            (option: any) =>
                                option.value == res.data.result.paymentStatus
                        )
                    )
                }
            })
            .catch((err) => {
                console.log(err)
            })
    }

    useEffect(() => {
        getOrderDetails()
    }, [])

    console.log(orderStatus)

    const getStatusClassName = (status: any) => {
        switch (status) {
            case 'PENDING':
                return 'bg-amber-100 text-amber-600 dark:bg-amber-500/20 dark:text-amber-100 border-0 rounded'
            case 'PLACED':
                return 'bg-blue-100 text-blue-600 dark:bg-blue-500/20 dark:text-blue-100 border-0 rounded'
            case 'READY TO SHIP':
                return 'bg-indigo-100 text-indigo-600 dark:bg-indigo-500/20 dark:text-indigo-100 border-0 rounded'
            case 'SHIPPED VIA ECO':
                return 'bg-lime-100 text-lime-600 dark:bg-lime-500/20 dark:text-lime-100 border-0 rounded'
            case 'SHIPPED VIA INHOUSE':
                return 'bg-lime-100 text-lime-600 dark:bg-lime-500/20 dark:text-lime-100 border-0 rounded'
            case 'OUT FOR DELIVERY':
                return 'bg-purple-100 text-purple-600 dark:bg-purple-500/20 dark:text-purple-100 border-0 rounded'
            case 'DELIVERED':
                return 'bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded'
            case 'FAILED':
                return 'bg-red-100 text-red-600 dark:bg-red-500/20 dark:text-red-100 border-0 rounded'
            case 'RETURNED':
                return 'bg-pink-100 text-pink-600 dark:bg-pink-500/20 dark:text-pink-100 border-0 rounded'
            case 'REFUNDED':
                return 'bg-violet-100 text-violet-600 dark:bg-violet-500/20 dark:text-violet-100 border-0 rounded'
            case 'CANCELLED':
                return 'bg-red-100 text-red-600 dark:bg-red-500/20 dark:text-red-100 border-0 rounded'
            case 'CONFIRMED':
                // You can either leave this blank or provide default styling
                return 'bg-gray-100 text-gray-600 dark:bg-gray-500/20 dark:text-gray-100 border-0 rounded'
            default:
                return 'bg-gray-100 text-gray-600 dark:bg-gray-500/20 dark:text-gray-100 border-0 rounded'
        }
    }

    return (
        <div className="p-4">
            <div className="flex justify-between">
                <div>
                    <h2 className="mb-4">Manage Orders</h2>
                    <Breadcrumb items={breadCrumbItems} />
                </div>

                {!statusesWhereCancelNotAllowed.includes(data?.orderStatus) && (
                    <div className="flex justify-end gap-4">
                        <Button
                            className="border-red-500 text-red-500"
                            onClick={openCancelModal}
                        >
                            Cancel Order
                        </Button>
                        <Link to={`/generate-invoice/${params.id}`}>
                            <Button variant="solid" color="green">
                                Generate Invoice
                            </Button>
                        </Link>
                    </div>
                )}
            </div>

            <div className="grid grid-cols-2 gap-4 my-2">
                <div>
                    <div className="font-bold text-lg mb-2">
                        Order Details ({data?.orderNo}) {" "}
                        <Tag
                            className={
                                getStatusClassName(data?.orderStatus)
                            }
                        >
                            {data?.orderStatus}
                        </Tag>
                    </div>
                    <Card>
                        <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">
                                Order Place on{' '}
                            </p>
                            <p className="font-bold text-black">
                                {new Date(data?.orderDate).toLocaleDateString(
                                    'en-GB'
                                )}
                            </p>
                        </div>
                        <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">
                                Payment Method{' '}
                            </p>
                            <p className="font-bold text-black">
                                {data?.paymentMethod}
                            </p>
                        </div>
                        {data?.paymentMethod == "ONLINE" && <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">
                                Payment Gatway{' '}
                            </p>
                            <p className="font-bold text-black">
                                {data?.gateway}
                            </p>
                        </div>}
                        <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">
                                Payment Status{' '}
                            </p>
                            <p className="font-bold text-black">
                                {data?.paymentStatus}
                            </p>
                        </div>
                        <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">
                                Shipping Method{' '}
                            </p>
                            <p className="font-bold text-black">
                                {data?.shippingMethod == "click"? "Click & Collect" : "Doorstep Delivery"}
                            </p>
                        </div>
                        <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">Order Total</p>
                            <p className="font-bold text-black">
                                AED {data?.total}
                            </p>
                        </div>
                        {/* <div className="flex justify-between mb-1">
              <p className="text-sm font-semibold">Shipping Method</p>
              <p className="font-bold text-black">NIL</p>
            </div> */}
                    </Card>
                </div>

                <div>
                    <p className="font-bold text-lg mb-2">
                        Order Amount Details
                    </p>
                    <Card>
                        <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">
                                Sub Total{' '}
                                <span className="text-xs text-black">
                                    ({data?.products?.length} items)
                                </span>
                            </p>
                            <p className="font-bold text-black">
                                AED {data?.baseTotal - (data?.vatAmount ?? 0)}
                            </p>
                        </div>
                        <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">VAT ({data?.vat}%)</p>
                            <p className="font-bold text-black">
                                {data?.vatAmount ? `AED ${data?.vatAmount}` : 'AED 0'}
                            </p>
                        </div>
                        {data?.savings ?
                            <>
                                <div className="flex justify-between mb-1">
                                    <p className="text-sm font-semibold">Coupon Discount</p>
                                    <p className="font-bold text-black">
                                        {data?.savings ? 'AED ' + data?.savings : 'NIL'}
                                    </p>
                                </div>
                                <div className="flex justify-between mb-1">
                                    <p className="text-sm font-semibold">Coupon Code</p>
                                    <p className="font-bold text-black">
                                        {data?.couponCode ? data?.couponCode : 'NIL'}
                                    </p>
                                </div>
                            </>
                            : ""
                        }
                        {data?.loyaltyDiscount ? <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">Loyalty Discount</p>
                            <p className="font-bold text-black">
                                {data?.loyaltyDiscount ? 'AED ' + data?.loyaltyDiscount : 'NIL'}
                            </p>
                        </div>: ""}
                        <div className="flex justify-between mb-1">
                            <p className="text-sm font-semibold">Shipping</p>
                            <p className="font-bold text-black">
                                {data?.shippingCharge
                                    ? 'AED' + data?.shippingCharge
                                    : 'NIL'}
                            </p>
                        </div>
                        <div className="flex justify-between mb-1">
                            <p className="text-md font-bold text-black">
                                Total
                            </p>
                            <p className="font-bold text-black">
                                AED {data?.total}
                            </p>
                        </div>
                    </Card>
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4 my-2">
                <Card>
                    <div className="relative">
                        <div className="flex items-center gap-2 mb-2">
                            <MdOutlineLocalShipping size={20} />
                            <h5>Shipping Address</h5>
                        </div>
                        {data?.address?.name ?
                            <>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.address?.name} ,
                                    {data?.address?.countryCode +
                                        ' ' +
                                        data?.address?.mobile}
                                </p>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.address?.street}, {data?.address?.suiteUnit}{' '}
                                    , {data?.address?.postalCode}
                                </p>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.address?.city}, {data?.address?.emirates},{' '}
                                    {data?.address?.country}
                                </p>
                            </>
                            :
                            <>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.addressDetails?.name} ,
                                    {data?.addressDetails?.countryCode +
                                        ' ' +
                                        data?.addressDetails?.mobile}
                                </p>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.addressDetails?.street}, {data?.addressDetails?.suiteUnit}{' '}
                                    , {data?.addressDetails?.postalCode}
                                </p>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.addressDetails?.city}, {data?.addressDetails?.emirates},{' '}
                                    {data?.addressDetails?.country}
                                </p>
                            </>
                        }

                        {(orderStatus?.value == "PLACED") && <CiEdit
                            size={25}
                            className="cursor-pointer text-red-500 absolute right-0 top-0"
                            onClick={() =>
                                openAddressModal(data?.address?.refid)
                            }
                        />}
                    </div>
                </Card>

                <Card>
                    <div className="relative">
                        <div className="flex items-center gap-2 mb-2">
                            <MdOutlineLocalShipping size={20} />
                            <h5>Billing Address</h5>
                        </div>
                        {data?.address?.name ?
                            <>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.address?.name} ,
                                    {data?.address?.countryCode +
                                        ' ' +
                                        data?.address?.mobile}
                                </p>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.address?.street}, {data?.address?.suiteUnit}{' '}
                                    , {data?.address?.postalCode}
                                </p>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.address?.city}, {data?.address?.emirates},{' '}
                                    {data?.address?.country}
                                </p>
                            </>
                            :
                            <>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.addressDetails?.name} ,
                                    {data?.addressDetails?.countryCode +
                                        ' ' +
                                        data?.addressDetails?.mobile}
                                </p>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.addressDetails?.street}, {data?.addressDetails?.suiteUnit}{' '}
                                    , {data?.addressDetails?.postalCode}
                                </p>
                                <p className="text-sm font-semibold mb-1">
                                    {data?.addressDetails?.city}, {data?.addressDetails?.emirates},{' '}
                                    {data?.addressDetails?.country}
                                </p>
                            </>
                        }

                        {/* <CiEdit
                            size={25}
                            className="cursor-pointer text-red-500 absolute right-0 top-0"
                            onClick={() =>
                                openAddressModal(data?.address?.refid)
                            }
                        /> */}
                    </div>
                </Card>
            </div>
            {data?.shippingMethod == "click" && <div className="grid grid-cols-2 gap-4 my-2">
                <Card>
                    <div className="relative">
                        <div className="flex items-center gap-2 mb-2">
                            <MdOutlineLocalShipping size={20} />
                            <h5>Store Information</h5>
                        </div>
                        <p>{data?.storeId?.name?.en}</p>
                        <p>{data?.storeId?.address?.en}</p>
                    </div>
                </Card>
            </div>}
            <div>
                {data?.orderStatus != 'CANCELLED' && (
                    <div className="flex my-2 gap-3">
                        {(data?.trackingId || data?.awb) ? <div className="">
                            <p>Tracking: <a className='text-blue-600 underline' href={`https://app.ecofreight.ae/en/tracking/${data?.trackingId}`}>{data?.trackingId}</a></p>
                            <p>AWB: <a className='text-blue-600 underline' href={data?.awb}>AWB</a></p>
                        </div> : ""}
                        <div className="flex gap-3 ml-auto">
                            <FormItem className="w-60" label="Order Status">
                                <Select
                                    value={updatedOrderStatus || orderStatus}
                                    options={orderStatusOptions}
                                    onChange={(value) =>
                                        handleOrderStatus(value, 'orderStatus')
                                    }
                                />
                            </FormItem>

                            <FormItem className="w-60" label="Payment Status">
                                <Select
                                    value={updatedPaymentStatus || paymentStatus}
                                    options={paymentStatusOptions}
                                    onChange={(value) =>
                                        handleOrderStatus(value, 'paymentStatus')
                                    }
                                />
                            </FormItem>

                            <Button
                                className="mt-5"
                                variant="solid"
                                color="green"
                                onClick={saveOrderStatus}
                            >
                                Save
                            </Button>
                        </div>
                    </div>
                )}

                <Card>
                    <div className="flex items-center gap-2 mb-2">
                        <BsBoxSeam size={20} />
                        <p className="font-bold text-lg text-black">
                            Product Details
                        </p>
                    </div>
                    {data?.products?.map((product: any) => (
                        <div
                            key={product?._id}
                            className="flex items-center space-x-4 bg-white p-4"
                        >
                            <img
                                alt="Product"
                                className="h-16 w-16 rounded object-cover aspect-[64/64]"
                                src={imageBase + product?.product?.thumbnail}
                            />
                            <div className="flex flex-col justify-between flex-grow">
                                <span className="text-sm font-semibold">
                                    {product?.product?.name?.en}
                                </span>
                                <span className="text-xs text-gray-500">
                                    Color: {product?.product?.color?.name.en}
                                </span>
                                <span className="text-xs text-gray-500">
                                    SKU: {product?.product?.sku}
                                </span>
                                {/* <span className="text-xs text-gray-500">Size: M</span> */}
                            </div>
                            {product?.lensDetails && (
                                <div>
                                    <Button
                                        color="red"
                                        variant="twoTone"
                                        onClick={() =>
                                            toggleLensModal(product.lensDetails)
                                        }
                                    >
                                        Lens Details
                                    </Button>
                                </div>
                            )}
                            {product?.contactLens && (
                                <div>
                                    <Button
                                        color="red"
                                        variant="twoTone"
                                        onClick={() =>
                                            toggleContactLensModal(product.contactLens)
                                        }
                                    >
                                        Contact Lens Details
                                    </Button>
                                </div>
                            )}
                            <div className="flex flex-col items-end">
                                <span className="text-xs text-gray-500">
                                    AED {product?.price} x {product?.quantity}
                                </span>
                                <span className="text-sm font-semibold">
                                    AED {product?.total}
                                </span>
                            </div>
                        </div>
                    ))}

                    <div className="bg-white p-4 shadow-md">
                        <h2 className="font-semibold text-lg mb-4">
                            Order history
                        </h2>
                        <div className=" grid grid-cols-4 gap-4">
                            {data?.history?.map((history: any) => (
                                <div
                                    key={history?._id}
                                    className="mt-2 bg-gray-100 border p-4"
                                >
                                    <h3 className="font-semibold text-sm mb-1">
                                        {history?.status}
                                    </h3>
                                    <p className="text-xs text-gray-600">
                                        {new Date(
                                            history?.date
                                        ).toLocaleDateString('en', {
                                            day: 'numeric',
                                            month: 'short',
                                            year: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            second: '2-digit',
                                        })}
                                    </p>
                                </div>
                            ))}
                        </div>
                    </div>
                </Card>
            </div>

            <DeleteModal
                isOpen={cancelModalOpen}
                onClose={closeCancelModal}
                onConfirm={confirmCancel}
                title="Cancel Order"
                content="Are you sure you want to cancel this order?"
            />

            <Dialog
                className={'overflow-hidden'}
                width={900}
                height={600}
                isOpen={addressModalOpen}
                onClose={closeAddressModal}
            >
                <ManageAddress data={address} closeModal={closeAddressModal} />
            </Dialog>

            <LensDetailsModal
                isOpen={lensModalOpen}
                onClose={() => {
                    setLensModalOpen(false)
                    setSelectedLensDetails(null)
                }}
                lensDetails={selectedLensDetails}
            />

            <ContactLensDetails
                isOpen={contactLensModalOpen}
                onClose={() => {
                    setContactLensModalOpen(false)
                    setSelectedContactLensDetails(null)
                }}
                lensDetails={selectedContactLensDetails}
            />
        </div>
    )
}
