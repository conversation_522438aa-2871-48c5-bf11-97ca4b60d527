import { Button, FormItem, toast, Select, Upload, Input } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { RichTextEditor } from '@/components/shared'
import Breadcrumb from '../modals/BreadCrumb'

const imageUrl = import.meta.env.VITE_ASSET_URL

/* eslint-disable */
export default function ManageBlogs() {
    const [imageFile, setImageFile] = useState<any>([])
    const [imageError, setImageError] = useState<any>(null)
    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Blogs', url: '/blogs' },
        { title: 'Manage Blogs', url: '' },
    ]

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const handleImageChange = (files: any) => {
        setImageFile(files)
    }

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    useEffect(() => {
        if (params.id) {
            api.get(endpoints.blogDetail + params.id)
                .then((res) => {
                    if (res.status == 200) {
                        setValue('titleEn', res.data.result.title.en)
                        setValue('titleAr', res.data.result.title.ar)
                        setValue('summaryEn', res.data.result.summary.en)
                        setValue('summaryAr', res.data.result.summary.ar)
                        setValue('contentEn', res.data.result.content.en)
                        setValue('contentAr', res.data.result.content.ar)
                        setValue('authorEn', res.data.result.author?.en)
                        setValue('authorAr', res.data.result.author?.ar)
                        setValue('isActive', {
                            value: res.data.result.isActive,
                            label: res.data.result.isActive ? 'True' : 'False',
                        })
                        setImageFile([imageUrl + res?.data?.result?.image])
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }, [params.id])

    const onSubmit = (data: any) => {
        if (imageFile.length == 0) {
            setImageError('Image is required')
            return
        }

        setImageError(null)
        const formData = new FormData()
        formData.append('title[en]', data.titleEn)
        formData.append('title[ar]', data.titleAr)
        formData.append('summary[en]', data.summaryEn)
        formData.append('summary[ar]', data.summaryAr)
        formData.append('content[en]', data.contentEn)
        formData.append('content[ar]', data.contentAr)
        formData.append('author[en]', data.authorEn)
        formData.append('author[ar]', data.authorAr)
        if (params.id) formData.append('isActive', data.isActive.value)
        if (imageFile.length > 0) formData.append('image', imageFile[0])

        if (params.id) {
            formData.append('refid', params.id)
            api.post(endpoints.updateBlog, formData).then((res) => {
                if (res.status == 200) {
                    if (res?.data?.errorCode == 0) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/blogs')
                    }
                } else {
                    toast.push(
                        <Notification
                            type="warning"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
            })
        } else {
            api.post(endpoints.createBlog, formData)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/blogs')
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params.id ? 'Edit' : 'Add '} Blogs</h3>
            <Breadcrumb items={breadcrumbItems} />
            <h5>Title</h5>
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="English">
                    <Controller
                        name="titleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.titleEn
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.titleEn && (
                        <small className="text-red-600 py-3">
                            {errors.titleEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="titleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.titleAr && (
                        <small className="text-red-600 py-3">
                            {errors.titleAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <h5>Summary</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        name="summaryEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Summary is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.summaryEn
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.summaryEn && (
                        <small className="text-red-600 py-3">
                            {errors.summaryEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="summaryAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Summary is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.summaryAr && (
                        <small className="text-red-600 py-3">
                            {errors.summaryAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <div>
                <FormItem label="Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        fileList={imageFile}
                        onChange={handleImageChange}
                        ratio={[2250, 766]}
                    />
                    {imageError && (
                        <small className="text-red-600">{imageError}</small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Author Name (English)">
                    <Controller
                        name="authorEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Author Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${errors.author
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.author && (
                        <small className="text-red-600 py-3">
                            {errors.author.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Author Name (Arabic)">
                    <Controller
                        name="authorAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Author Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${errors.author
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                </FormItem>
            </div>

            <div>
                <FormItem label="Content English">
                    <Controller
                        name="contentEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Content is required' }}
                        render={({ field }) => (
                            <RichTextEditor {...field} modules={'image'} />
                        )}
                    />
                    {errors.contentEn && (
                        <small className="text-red-600 py-3">
                            {errors.contentEn.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div>
                <FormItem label="Content Arabic">
                    <Controller
                        name="contentAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Content is required' }}
                        render={({ field }) => (
                            <RichTextEditor {...field} modules={'image'} />
                        )}
                    />
                    {/* {errors.contentAr && (
                        <small className="text-red-600 py-3">
                            {errors.contentAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            {params.id && (
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="is Active ?">
                        <Controller
                            name="isActive"
                            defaultValue=""
                            control={control}
                            render={({ field }) => (
                                <Select
                                    defaultValue=""
                                    options={options}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                </div>
            )}

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
