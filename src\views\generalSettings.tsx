/* eslint-disable */
import { Button, FormItem, Select, Tag, toast, Upload } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { AiOutlineMinus, AiOutlinePlus, AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from './modals/BreadCrumb'
import { HiX } from 'react-icons/hi'
import { Dropzone } from '@/components/shared/Dropzone'
import { title } from 'process'

const options = [
    { value: 'true', label: 'Active' },
    { value: 'false', label: 'Inactive' },
]

const optionsStore = [
    { value: 'true', label: 'Iframe' },
    { value: 'false', label: 'Custom' },
]

export default function GeneralSettings() {
    const [isUpdate, setIsUpdate] = useState(false)
    const [imageFile, setImageFile] = useState<any>([])
    const [imageError, setImageError] = useState<any>(null)
    const baseUrl = import.meta.env.VITE_ASSET_URL
    const [submitted, setSubmitted] = useState(false)
    const [iconPreviews, setIconPreviews] = useState<string[]>([])
    const [ogImageFile, setOgImageFile] = useState<string[]>([])
    const breadcrumbItems = [{ title: 'General Settings', url: '' }]

    const handleImageUpload = (files: any) => {
        setImageFile(files)
    }

    const handleIconUpload = (e: any, index: number) => {
        const image = e.target.files[0]
        if (image) {
            const formData = new FormData()
            formData.append('video', image)

            api.post(endpoints.videoUpload, formData)
                .then((res) => {
                    const imageUrl = res?.data?.videoUrl
                    setValue(`socialMedia.${index}.imageUrl`, imageUrl)
                    setIconPreviews((prevPreviews) => {
                        const newPreviews = [...prevPreviews]
                        newPreviews[index] = imageUrl
                        return newPreviews
                    })
                })
                .catch((err) => {
                    console.log('err', err)
                })
        }
    }

    const handleOgImageUpload = (files: any) => {
        setOgImageFile(files)
    }

    const {
        handleSubmit,
        control,
        setValue,
        getValues,
        watch,
        formState: { errors },
    } = useForm<any>()

    const {
        fields: announcementFields,
        append: announcementAppend,
        remove: announcementRemove,
    } = useFieldArray({
        control,
        name: 'announcement',
    })

    const {
        fields: socialMediaFields,
        append: socialMediaAppend,
        remove: socialMediaRemove,
    } = useFieldArray({
        control,
        name: 'socialMedia',
    })

    useEffect(() => {
        api.get(endpoints.settings)
            .then((res) => {
                if (res?.status == 200) {
                    setIsUpdate(true)
                    if (res?.data?.errorCode == 0) {
                        const data = res?.data?.result[0]
                        setValue('addressEn', data?.contact?.address?.en)
                        setValue('addressAr', data?.contact?.address?.ar)
                        setValue('email', data?.contact?.email)
                        setValue('mobileOne', data?.contact?.mobileOne)
                        setValue('mobileTwo', data?.contact?.mobileTwo)
                        setValue('copyRightEn', data?.copyRight?.en)
                        setValue('copyRightAr', data?.copyRight?.ar)
                        setValue('analyticsId', data?.analyticsId)
                        setValue('tagManagerId', data?.tagManagerId)
                        setValue('pixelId', data?.pixelId)
                        setValue('tryCartBtnTextEn', data?.tryCartBtnText?.en)
                        setValue('tryCartBtnTextAr', data?.tryCartBtnText?.ar)
                        setValue('search', data?.popularSearch)
                        setValue('metaTitleEn', data?.seoDetails?.title?.en)
                        setValue('metaTitleAr', data?.seoDetails?.title?.ar)
                        setValue('metaDescriptionEn', data?.seoDetails?.description?.en)
                        setValue('metaDescriptionAr', data?.seoDetails?.description?.ar)
                        setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en)
                        setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar)
                        setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en)
                        setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar)
                        setOgImageFile([data?.seoDetails?.ogImage])

                        setValue('tamara', data?.tamara ? options[0] : options[1])
                        setValue('tabby', data?.tabby ? options[0] : options[1])
                        setValue('clickAndCollect', data?.clickAndCollect ? options[0] : options[1])
                        setValue('isStoreLocatorIframe', data?.isStoreLocatorIframe ? optionsStore[0] : optionsStore[1])

                        setValue('isGiftWrapping', data?.isGiftWrapping ? options[0] : options[1])
                        setValue('giftWrappingFee', data?.giftWrappingFee ?? 0)

                        if (data?.socialMedia.length > 0) {
                            setValue(
                                'socialMedia',
                                data?.socialMedia?.map((socialMedia: any) => ({
                                    name: socialMedia?.name,
                                    link: socialMedia?.link,
                                    imageUrl: socialMedia?.icon,
                                }))
                            )
                            setIconPreviews(
                                data?.socialMedia?.map(
                                    (socialMedia: any) => socialMedia?.icon
                                )
                            )
                        }
                        if (data?.text.length > 0) {
                            setValue(
                                'announcement',
                                data?.text?.map((announcement: any) => ({
                                    textEn: announcement?.en,
                                    textAr: announcement?.ar,
                                }))
                            )
                        }
                        setImageFile([baseUrl + data?.logo])
                        if (data?.socialMedia.length == 0) socialMediaAppend({})
                    }
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
        if (isUpdate == false) announcementAppend({})
    }, [submitted])

    const onSubmit = (value: any) => {
        if (imageFile.length == 0) {
            setImageError('Image is required')
            return
        }
        setImageError(null)

        const formData = new FormData()
        const data = {
            whatsappNumber: value.whatsapp,
            analyticsId: value.analyticsId,
            tagManagerId: value.tagManagerId,
            pixelId: value.pixelId,
            copyRight: {
                en: value.copyRightEn,
                ar: value.copyRightAr,
            },
            socialMedia: value.socialMedia.map((socialMedia: any) => ({
                name: socialMedia?.name,
                link: socialMedia?.link,
                icon: socialMedia?.imageUrl,
            })),
            contact: {
                address: {
                    en: value.addressEn,
                    ar: value.addressAr,
                },
                email: value.email,
                mobileOne: value.mobileOne,
                mobileTwo: value.mobileTwo,
            },
            text: value.announcement.map((announcement: any) => ({
                en: announcement?.textEn,
                ar: announcement?.textAr,
            })),
            popularSearch: value.search,
            tryCartBtnText: {
                en: value.tryCartBtnTextEn,
                ar: value.tryCartBtnTextAr,
            },
            seoDetails: {
                title: {
                    en: value.metaTitleEn,
                    ar: value.metaTitleAr,
                },
                description: {
                    en: value.metaDescriptionEn,
                    ar: value.metaDescriptionAr,
                },
                keywords: {
                    en: value.metaKeywordsEn,
                    ar: value.metaKeywordsAr,
                },
                canonical: {
                    en: value.metaCanonicalUrl,
                    ar: value.metaCanonicalUrlAr,
                },
                ogImage: ogImageFile[0]
            },
            tamara: value.tamara?.value == "true" ? true : false,
            tabby: value.tabby?.value == "true" ? true : false,
            clickAndCollect: value.clickAndCollect?.value == "true" ? true : false,
            isStoreLocatorIframe: value.isStoreLocatorIframe?.value == "true" ? true : false,
            // isGiftWrapping: value.isGiftWrapping?.value == "true" ? true : false,
            // giftWrappingFee: value.giftWrappingFee,
        }
        formData.append('logo', imageFile[0])
        formData.append('data', JSON.stringify(data))

        if (isUpdate) {
            api.post(endpoints.updateSettings, formData).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setSubmitted(true)
                }
            })
        } else {
            api.post(endpoints.createSettings, formData)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        setSubmitted(true)
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    const onkeyword = (e: any) => {
        if (e.key === 'Enter') {
            e.preventDefault()
            const newKeyword = e.currentTarget.value.trim()
            if (newKeyword) {
                let values = getValues("search") ?? [];
                values.push(newKeyword);
                setValue('search', values)
                e.currentTarget.value = ''
            }
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">General Settings</h3>
            <Breadcrumb items={breadcrumbItems} />
            <h5 className="mb-2">Header Announcement</h5>
            <ul className="mt-4">
                {announcementFields.map((item, index) => {
                    return (
                        <li key={item.id}>
                            <div className="grid grid-cols-3 items-center gap-4">
                                <FormItem label="English">
                                    <Controller
                                        name={`announcement.${index}.textEn`}
                                        control={control}
                                        defaultValue={''}
                                        rules={{
                                            required: 'Title is required',
                                        }}
                                        render={({ field }) => (
                                            <Input
                                                defaultValue={''}
                                                type="text"
                                                width={'full'}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {errors?.announcement &&
                                        Array.isArray(errors.announcement) &&
                                        errors.announcement[index]?.textEn && (
                                            <small className="text-red-600 py-3">
                                                {
                                                    errors.announcement[index]
                                                        .textEn
                                                        .message as string
                                                }
                                            </small>
                                        )}
                                </FormItem>

                                <FormItem label="Arabic">
                                    <Controller
                                        name={`announcement.${index}.textAr`}
                                        control={control}
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input
                                                dir="rtl"
                                                defaultValue={''}
                                                type="text"
                                                {...field}
                                            />
                                        )}
                                    />
                                    {/* {errors?.announcement &&
                                        Array.isArray(errors.announcement) &&
                                        errors.announcement[index]?.textAr && (
                                            <small className="text-red-600 py-3">
                                                {
                                                    errors.announcement[index]
                                                        .textAr
                                                        .message as string
                                                }
                                            </small>
                                        )} */}
                                </FormItem>

                                <Button
                                    size="sm"
                                    shape="circle"
                                    type="button"
                                    icon={<AiOutlineMinus />}
                                    onClick={() => announcementRemove(index)}
                                ></Button>
                            </div>
                        </li>
                    )
                })}
                <Button
                    variant="solid"
                    type="button"
                    className="mb-4"
                    icon={<AiOutlinePlus />}
                    onClick={() => {
                        announcementAppend({})
                    }}
                >
                    Add More Announcement
                </Button>
            </ul>

            <h5 className='mt-4'>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <FormItem label="Meta OG Image">
                <Dropzone ratio={[1074, 616]} previews={ogImageFile} onFileUrlChange={(e) => { setOgImageFile([e]) }} />
            </FormItem>

            <h5 className="mb-2">Popular Searches</h5>
            <div>
                <FormItem label="Tags">
                    <Controller
                        control={control}
                        name="search"
                        render={({ field }) => (
                            <div>
                                <Input
                                    type="text"
                                    onKeyPress={(e) => {
                                        onkeyword(e)
                                    }}
                                />
                            </div>
                        )}
                    />
                    <div className="flex flex-wrap gap-2 mt-2">
                        {watch('search')?.map((keyword: string, index: number) => (
                            <Tag
                                key={index}
                                className="bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded"
                                suffix={
                                    <HiX
                                        className="ml-1 rtl:mr-1 cursor-pointer"
                                        onClick={() => {
                                            const updatedKeywords =
                                                getValues('search')?.filter(
                                                    (_: string, i: number) =>
                                                        i !== index
                                                )
                                            setValue(
                                                'search',
                                                updatedKeywords
                                            )
                                        }}
                                    />
                                }
                            >
                                {keyword}
                            </Tag>
                        ))}
                    </div>
                </FormItem>
            </div>

            {/* <h5>Try Cart Button</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        name="tryCartBtnTextEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'This field is required' }}
                        render={({ field }) => (
                            <Input defaultValue={''} type="text" {...field} />
                        )}
                    />
                    {errors.tryCartBtnTextEn && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.tryCartBtnTextEn.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="tryCartBtnTextAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'This field is required' }}
                        render={({ field }) => (
                            <Input defaultValue={''} type="text" {...field} />
                        )}
                    />
                    {errors.tryCartBtnTextAr && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.tryCartBtnTextAr.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div> */}

            <h5 className="mb-2">Contact Details</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Address (English)">
                    <Controller
                        name="addressEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Address is required' }}
                        render={({ field }) => (
                            <Input defaultValue={''} {...field} textArea />
                        )}
                    />
                    {errors.addressEn && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.addressEn.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Address (Arabic)">
                    <Controller
                        name="addressAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Address is required' }}
                        render={({ field }) => (
                            <Input
                                defaultValue={''}
                                dir="rtl"
                                textArea
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.addressAr && (
                        <small className="text-red-600 py-3">
                            {errors.addressAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Email">
                    <Controller
                        name="email"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Email is required' }}
                        render={({ field }) => (
                            <Input defaultValue={''} type="text" {...field} />
                        )}
                    />
                    {errors.email && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.email.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Mobile">
                    <Controller
                        name="mobileOne"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Mobile is required' }}
                        render={({ field }) => (
                            <Input defaultValue={''} type="text" {...field} />
                        )}
                    />
                    {errors.mobileOne && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.mobileOne.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Alternate Mobile">
                    <Controller
                        name="mobileTwo"
                        control={control}
                        defaultValue=""
                        rules={{}}
                        render={({ field }) => (
                            <Input defaultValue={''} type="text" {...field} />
                        )}
                    />
                    {errors.mobileTwo && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.mobileTwo.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Whatsapp Number">
                    <Controller
                        name="whatsapp"
                        control={control}
                        defaultValue=""
                        rules={{}}
                        render={({ field }) => (
                            <Input defaultValue={''} type="text" {...field} />
                        )}
                    />
                    {errors.whatsapp && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.whatsapp.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>

            <h5 className="mb-2">Copy Right Text</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        name="copyRightEn"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Input defaultValue={''} type="text" {...field} />
                        )}
                    />
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="copyRightAr"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                defaultValue={''}
                                type="text"
                                {...field}
                            />
                        )}
                    />
                </FormItem>
            </div>

            <h5 className="mb-2">Website Logo</h5>
            <div>
                <FormItem label="Logo">
                    <Upload
                        draggable
                        ratio={[153, 35]}
                        uploadLimit={1}
                        accept="image/* image/svg+xml"
                        fileList={imageFile}
                        onChange={handleImageUpload}
                    />
                    {imageError && (
                        <small className="text-red-600">{imageError}</small>
                    )}
                </FormItem>
            </div>

            <h5>Analytics</h5>
            <div className="grid grid-cols-3 gap-4">
                <FormItem label="Google Analytics Id">
                    <Controller
                        name="analyticsId"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Input
                                placeholder="Eg:UA-XXXXXX-Y"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem label="Tag Manager">
                    <Controller
                        name="tagManagerId"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Input
                                placeholder="Eg:GTM-XXXXXX"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem label="Pixel">
                    <Controller
                        name="pixelId"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Input
                                placeholder="Eg:123456789012345"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                </FormItem>
            </div>

            <h5 className="mb-2">Social Media Links</h5>
            <ul className="mt-4">
                {socialMediaFields.map((item, index) => {
                    return (
                        <li key={item.id}>
                            <div className="grid grid-cols-4 gap-2">
                                <FormItem label="Name">
                                    <Controller
                                        name={`socialMedia.${index}.name`}
                                        control={control}
                                        defaultValue={''}
                                        rules={{
                                            required: 'Title is required',
                                        }}
                                        render={({ field }) => (
                                            <Input
                                                defaultValue={''}
                                                type="text"
                                                width={'full'}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {errors?.socialMedia &&
                                        Array.isArray(errors.socialMedia) &&
                                        errors.socialMedia[index]?.name && (
                                            <small className="text-red-600 py-3">
                                                {
                                                    errors.socialMedia[index]
                                                        .name.message as string
                                                }
                                            </small>
                                        )}
                                </FormItem>

                                <FormItem label="Link">
                                    <Controller
                                        name={`socialMedia.${index}.link`}
                                        control={control}
                                        defaultValue={''}
                                        rules={{ required: 'Link is required' }}
                                        render={({ field }) => (
                                            <Input
                                                defaultValue={''}
                                                type="text"
                                                width={'full'}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {errors?.socialMedia &&
                                        Array.isArray(errors.socialMedia) &&
                                        errors.socialMedia[index]?.link && (
                                            <small className="text-red-600 py-3">
                                                {
                                                    errors.socialMedia[index]
                                                        .link.message as string
                                                }
                                            </small>
                                        )}
                                </FormItem>

                                <FormItem label="Icon (Preferred Size: 20x20 px)">
                                    <Controller
                                        name={`socialMedia.${index}.icon`}
                                        control={control}
                                        defaultValue={null}
                                        render={({ field }) => (
                                            <div>
                                                <Input
                                                    type="file"
                                                    {...field}
                                                    onChange={(e) => {
                                                        field.onChange(e)
                                                        handleIconUpload(
                                                            e,
                                                            index
                                                        )
                                                    }}
                                                />

                                                {iconPreviews[index] && (
                                                    <img
                                                        className="mt-2 w-20 mx-auto object-cover object-top"
                                                        src={
                                                            baseUrl +
                                                            iconPreviews[index]
                                                        }
                                                        alt={`Image Preview ${index}`}
                                                    />
                                                )}
                                            </div>
                                        )}
                                    />
                                </FormItem>

                                <Button
                                    size="sm"
                                    shape="circle"
                                    type="button"
                                    icon={<AiOutlineMinus />}
                                    className="float-right mt-6"
                                    onClick={() => socialMediaRemove(index)}
                                ></Button>
                            </div>
                        </li>
                    )
                })}
                <Button
                    variant="solid"
                    type="button"
                    className="mb-4"
                    icon={<AiOutlinePlus />}
                    onClick={() => {
                        socialMediaAppend({})
                    }}
                >
                    Add More Social Media
                </Button>
            </ul>

            <div className="grid grid-cols-2 gap-2">
                <FormItem label="Tamara">
                    <Controller
                        name="tamara"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select options={options} {...field} />
                        )}
                    />
                </FormItem>
            </div>
            <div className="grid grid-cols-2 gap-2">
                <FormItem label="Tabby">
                    <Controller
                        name="tabby"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select options={options} {...field} />
                        )}
                    />
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-2">
                <FormItem label="Click And Collect">
                    <Controller
                        name="clickAndCollect"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select options={options} {...field} />
                        )}
                    />
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-2">
                <FormItem label="Store Locator">
                    <Controller
                        name="isStoreLocatorIframe"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select options={optionsStore} {...field} />
                        )}
                    />
                </FormItem>
            </div>

            {/* <div className="grid grid-cols-2 gap-4">
                <FormItem label="Gift wrapping">
                    <Controller
                        name="isGiftWrapping"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select options={options} {...field} />
                        )}
                    />
                </FormItem>
                <FormItem label="Gift wrapping Fee">
                    <Controller
                        control={control}
                        name="giftWrappingFee"
                        rules={{ min: { value: 0, message: "Values cannot be less than 0" } }}
                        render={({ field }) => <Input type="number" {...field} />}
                    />
                    {errors.giftWrappingFee && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.giftWrappingFee.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div> */}

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
