import { lazy } from 'react'
import authRoute from './authRoute'
import type { Routes } from '@/@types/routes'

export const publicRoutes: Routes = [...authRoute]

export const protectedRoutes = [
    {
        key: 'home',
        path: '/home',
        component: lazy(() => import('@/views/Home')),
        authority: [],
    },
    {
        key: 'access-denied',
        path: '/access-denied',
        component: lazy(() => import('@/components/shared/AccessDenied')),
        authority: [],
    },
    {
        key: 'stores',
        path: '/stores',
        component: lazy(() => import('@/views/stores/StoresListing')),
        authority: [],
    },
    {
        key: 'addStore',
        path: '/manage-store',
        component: lazy(() => import('@/views/stores/AddStore')),
        authority: [],
    },
    {
        key: 'addStore',
        path: '/manage-store/:id',
        component: lazy(() => import('@/views/stores/AddStore')),
        authority: [],
    },
    {
        key: 'groupMenu.single',
        path: '/catalog/brands',
        component: lazy(() => import('@/views/catalog/brands/Brands')),
        authority: [],
    },
    {
        key: 'groupMenu.single',
        path: '/catalog/manage-brand',
        component: lazy(() => import('@/views/catalog/brands/ManageBrands')),
        authority: [],
    },
    {
        key: 'groupMenu.single',
        path: '/catalog/manage-brand/:id',
        component: lazy(() => import('@/views/catalog/brands/ManageBrands')),
        authority: [],
    },
    {
        key: 'terms',
        path: '/pages/terms',
        component: lazy(() => import('@/views/pages/Terms')),
        authority: [],
    },
    {
        key: 'privacyPolicy',
        path: '/pages/privacy-policy',
        component: lazy(() => import('@/views/pages/PrivacyPolicy')),
        authority: [],
    },
    {
        key: 'cookiePolicy',
        path: '/pages/cookie-policy',
        component: lazy(() => import('@/views/pages/CookiePolicy')),
        authority: [],
    },
    {
        key: 'refundPolicy',
        path: '/pages/refund-policy',
        component: lazy(() => import('@/views/pages/RefundPolicy')),
        authority: [],
    },
    {
        key: 'shippingPolicy',
        path: '/pages/shipping-policy',
        component: lazy(() => import('@/views/pages/ShippingPolicy')),
        authority: [],
    },
    {
        key: 'returnPolicy',
        path: '/pages/return-policy',
        component: lazy(() => import('@/views/pages/ReturnPolicy')),
        authority: [],
    },
    {
        key: 'aboutUs',
        path: '/pages/about-us',
        component: lazy(() => import('@/views/pages/AboutUs')),
        authority: [],
    },
    {
        key: 'categories',
        path: '/catalog/categories',
        component: lazy(() => import('@/views/catalog/categories/CategoryList')),
        authority: [],
    },
    {
        key: 'categories',
        path: '/catalog/manage-categories',
        component: lazy(() => import('@/views/catalog/categories/ManageCategory')),
        authority: [],
    },
    {
        key: 'categories',
        path: '/catalog/manage-categories/:id',
        component: lazy(() => import('@/views/catalog/categories/ManageCategory')),
        authority: [],
    },
    {
        key: 'categories',
        path: '/catalog/manage-sub-categories/:parent',
        component: lazy(() => import('@/views/catalog/categories/ManageSubCategory')),
        authority: [],
    },
    {
        key: 'categories',
        path: '/catalog/manage-sub-categories/:parent/:id',
        component: lazy(() => import('@/views/catalog/categories/ManageSubCategory')),
        authority: [],
    },
    {
        key: 'admins',
        path: '/admins',
        component: lazy(() => import('@/views/adminUser/Admins')),
        authority: [],
    },
    {
        key: 'admins',
        path: '/manage-admins',
        component: lazy(() => import('@/views/adminUser/ManageAdmins')),
        authority: [],
    },
    {
        key: 'admins',
        path: '/manage-admins/:id',
        component: lazy(() => import('@/views/adminUser/ManageAdmins')),
        authority: [],
    },
    {
        key: 'customers',
        path: '/customers',
        component: lazy(() => import('@/views/customers/CustomerList')),
        authority: [],
    },
    {
        key: 'customers',
        path: '/manage-customers',
        component: lazy(() => import('@/views/customers/ManageCustomer')),
        authority: [],
    },
    {
        key: 'customers',
        path: '/manage-customers/:id',
        component: lazy(() => import('@/views/customers/ManageCustomer')),
        authority: [],
    },
    {
        key: 'products',
        path: '/catalog/products',
        component: lazy(() => import('@/views/catalog/products/Products')),
        authority: [],
    },
    {
        key: 'products',
        path: '/catalog/manage-products/:type',
        component: lazy(() => import('@/views/catalog/products/ManageProductIndex')),
        authority: [],
    },
    {
        key: 'products',
        path: '/catalog/manage-products/:type/:id',
        component: lazy(() => import('@/views/catalog/products/ManageProductIndex')),
        authority: [],
    },
    {
        key: 'products',
        path : '/products/variants/:id',
        component: lazy(() => import('@/views/catalog/products/Variants')),
        authority: [],
    },
    {
        key: 'products',
        path: '/catalog/manage-products/:type/:id/:main',
        component: lazy(() => import('@/views/catalog/products/ManageProductIndex')),
        authority: [],
    },
    {
        key: 'attribute',
        path: '/catalog/attributes',
        component: lazy(() => import('@/views/catalog/attributes/AttributeList')),
        authority: [],
    },
    {
        key: 'attribute',
        path: '/catalog/manage-attributes',
        component: lazy(() => import('@/views/catalog/attributes/ManageAttribute')),
        authority: [],
    },
    {
        key: 'attribute',
        path: '/catalog/manage-attributes/:id',
        component: lazy(() => import('@/views/catalog/attributes/ManageAttribute')),
        authority: [],
    },
    {
        key: 'ageGroup',
        path: '/catalog/age-group',
        component: lazy(() => import('@/views/catalog/ageGroup/AgeGroup')),
        authority: [],
    },
    {
        key: 'ageGroup',
        path: '/catalog/manage-age-group',
        component: lazy(() => import('@/views/catalog/ageGroup/ManageAgeGroup')),
        authority: [],
    },
    {
        key: 'ageGroup',
        path: '/catalog/manage-age-group/:id',
        component: lazy(() => import('@/views/catalog/ageGroup/ManageAgeGroup')),
        authority: [],
    },
    {
        key: 'blogs',
        path: '/blogs',
        component: lazy(() => import('@/views/blogs/Blogs')),
        authority: [],
    },
    {
        key: 'blogs',
        path: '/manage-blogs',
        component: lazy(() => import('@/views/blogs/ManageBlogs')),
        authority: [],
    },
    {
        key: 'blogs',
        path: '/manage-blogs/:id',
        component: lazy(() => import('@/views/blogs/ManageBlogs')),
        authority: [],
    },
    {
        key: 'collections',
        path: '/catalog/collections',
        component: lazy(() => import('@/views/catalog/collections/Collections')),
        authority: [],
    },
    {
        key: 'collections',
        path: '/catalog/manage-collections',
        component: lazy(() => import('@/views/catalog/collections/ManageCollections')),
        authority: [],
    },
    {
        key: 'collections',
        path: '/catalog/manage-collections/:id',
        component: lazy(() => import('@/views/catalog/collections/ManageCollections')),
        authority: [],
    },
    {
        key: 'contactUs',
        path: '/contact-us-enquiries',
        component: lazy(() => import('@/views/contact/Enquiries')),
        authority: [],
    },
    {
        key: 'contactUs',
        path: '/contact-banner',
        component: lazy(() => import('@/views/contact/ContactBanner')),
        authority: [],
    },
    {
        key: 'insurance',
        path: '/insurance-enquiries',
        component: lazy(() => import('@/views/insurance/InsuranceEnquiries')),
        authority: [],
    },
    {
        key: 'insurance',
        path: '/insurance-provider',
        component: lazy(() => import('@/views/insurance/Providers')),
        authority: [],
    },
    {
        key: 'insurance',
        path: '/manage-insurance-provider',
        component: lazy(() => import('@/views/insurance/ManageProviders')),
        authority: [],
    },
    {
        key: 'insurance',
        path: '/manage-insurance-provider/:id',
        component: lazy(() => import('@/views/insurance/ManageProviders')),
        authority: [],
    },
    {
        key: 'insurance',
        path: '/insurance-contents',
        component: lazy(() => import('@/views/insurance/Contents')),
        authority: [],
    },
    {
        key: 'faq',
        path: '/faq',
        component: lazy(() => import('@/views/pages/Faq')),
        authority: [],
    },
    {
        key: 'dashboadDnd',
        path: '/dashboard-rearrange',
        component: lazy(() => import('@/views/DashboardDnd')),
        authority: [],
    },
    {
        key: 'cart',
        path: '/cart',
        component: lazy(() => import('@/views/cart/CartList')),
        authority: [],
    },
    {
        key: 'headerMenu',
        path: '/header-menu',
        component: lazy(() => import('@/views/headerMenu/HeaderMenuList')),
        authority: [],
    },
    {
        key: 'headerMenu',
        path: '/manage-header-menu',
        component: lazy(() => import('@/views/headerMenu/ManageHeaderMenu')),
        authority: [],
    },
    {
        key: 'headerMenu',
        path: '/manage-header-menu/:id',
        component: lazy(() => import('@/views/headerMenu/ManageHeaderMenu')),
        authority: [],
    },
    {
        key: 'generalSettings',
        path: '/general-settings',
        component: lazy(() => import('@/views/generalSettings')),
        authority: [],
    },
    {
        key: 'additionalFees',
        path: '/additional-fees',
        component: lazy(() => import('@/views/AdditionalFees')),
        authority: [],
    },
    {
        key: 'vm-policy',
        path: '/vm-policy',
        component: lazy(() => import('@/views/VMPolicy')),
        authority: [],
    },
    {
        key: 'translation',
        path: '/translation',
        component: lazy(() => import('@/views/translation/Translation')),
        authority: [],
    },
    {
        key: 'subscription',
        path: '/subscription-plans',
        component: lazy(() => import('@/views/subscription/Plans')),
        authority: [],
    },
    {
        key: 'subscription',
        path: '/manage-subscription-plans',
        component: lazy(() => import('@/views/subscription/ManagePlans')),
        authority: [],
    },
    {
        key: 'subscription',
        path: '/manage-subscription-plans/:id',
        component: lazy(() => import('@/views/subscription/ManagePlans')),
        authority: [],
    },
    {
        key: 'subscription',
        path: '/subscriptions',
        component : lazy(() => import('@/views/subscription/SubscriptionsList')),
        authority: [],
    },
    {
        key: 'banners',
        path: '/banners',
        component: lazy(() => import('@/views/banners/Banners')),
        authority: [],
    },
    {
        key: 'banners',
        path: '/manage-banners',
        component: lazy(() => import('@/views/banners/ManageBanners')),
        authority: [],
    },
    {
        key: 'banners',
        path: '/manage-banners/:id',
        component: lazy(() => import('@/views/banners/ManageBanners')),
        authority: [],
    },
    {
        key: 'frameShapes',
        path: '/frame-shapes',
        component: lazy(() => import('@/views/frameShapes/FrameShape')),
        authority: [],
    },
    {
        key: 'frameShapes',
        path: '/manage-frame-shapes',
        component: lazy(() => import('@/views/frameShapes/ManageFrameShape')),
        authority: [],
    },
    {
        key: 'frameShapes',
        path: '/manage-frame-shapes/:id',
        component: lazy(() => import('@/views/frameShapes/ManageFrameShape')),
        authority: [],
    },
    {
        key: 'frameTypes',
        path: '/frame-types',
        component: lazy(() => import('@/views/frameTypes/FrameType')),
        authority: [],
    },
    {
        key: 'frameTypes',
        path: '/manage-frame-types',
        component: lazy(() => import('@/views/frameTypes/ManageFrameType')),
        authority: [],
    },
    {
        key: 'frameTypes',
        path: '/manage-frame-types/:id',
        component: lazy(() => import('@/views/frameTypes/ManageFrameType')),
        authority: [],
    },
    {
        key: 'trycart',
        path: '/try-cart',
        component: lazy(() => import('@/views/trycart/TrycartSettings')),
        authority: [],
    },
    {
        key: 'orders',
        path: '/orders',
        component: lazy(() => import('@/views/orders/Orders')),
        authority: [],
    },
    {
        key: 'orders',
        path: '/manage-orders/:id',
        component: lazy(() => import('@/views/orders/ManageOrders')),
        authority: [],
    },
    {
        key: 'orders',
        path: '/generate-invoice/:id',
        component: lazy(() => import('@/views/orders/Invoice')),
        authority: [],
    },
    {
        key: 'trycartorder',
        path: '/try-cart-order',
        component: lazy(() => import('@/views/orders/TryCartOrder')),
        authority: [],
    },
    {
        key: 'colors',
        path: '/colors',
        component: lazy(() => import('@/views/colors/ColorList')),
        authority: [],
    },
    {
        key: 'colors',
        path: '/manage-colors',
        component: lazy(() => import('@/views/colors/ManageColor')),
        authority: [],
    },
    {
        key: 'colors',
        path: '/manage-colors/:id',
        component: lazy(() => import('@/views/colors/ManageColor')),
        authority: [],
    },
    {
        key: 'sizes',
        path: '/sizes',
        component: lazy(() => import('@/views/sizes/SizeList')),
        authority: [],
    },
    {
        key: 'sizes',
        path: '/manage-sizes',
        component: lazy(() => import('@/views/sizes/ManageSize')),
        authority: [],
    },
    {
        key: 'sizes',
        path: '/manage-sizes/:id',
        component: lazy(() => import('@/views/sizes/ManageSize')),
        authority: [],
    },
    {
        key: 'contact-lens-sizes',
        path: '/contact-lens-sizes',
        component: lazy(() => import('@/views/contactLensSizes/SizeList')),
        authority: [],
    },
    {
        key: 'contact-lens-sizes',
        path: '/manage-contact-lens-sizes',
        component: lazy(() => import('@/views/contactLensSizes/ManageSize')),
        authority: [],
    },
    {
        key: 'contact-lens-sizes',
        path: '/manage-contact-lens-sizes/:id',
        component: lazy(() => import('@/views/contactLensSizes/ManageSize')),
        authority: [],
    },
    {
        key: 'lensPower',
        path: '/lens-power',
        component: lazy(() => import('@/views/lensPower/LensPower')),
        authority: [],
    },
    {
        key: 'lensPower',
        path: '/manage-lens-power/:type',
        component: lazy(() => import('@/views/lensPower/ManageLensPower')),
        authority: [],
    },
    {
        key: 'lensPower',
        path: '/manage-lens-power/:type/:id',
        component: lazy(() => import('@/views/lensPower/ManageLensPower')),
        authority: [],
    },
    {
        key: 'contactLens',
        path: '/contact-lens',
        component: lazy(() => import('@/views/pages/ContactLens')),
        authority: [],
    },
    {
        key: 'lensBrand',
        path: '/lens-brand',
        component: lazy(() => import('@/views/lensBrand/LensBrands')),
        authority: [],
    },
    {
        key: 'lensBrand',
        path: '/manage-lens-brand',
        component: lazy(() => import('@/views/lensBrand/ManageLensBrand')),
        authority: [],
    },
    {
        key: 'lensBrand',
        path: '/manage-lens-brand/:id',
        component: lazy(() => import('@/views/lensBrand/ManageLensBrand')),
        authority: [],
    },
    {
        key: 'lensType',
        path: '/lens-type',
        component: lazy(() => import('@/views/lensTypes/LensType')),
        authority: [],
    },
    {
        key: 'lensType',
        path: '/manage-lens-type',
        component: lazy(() => import('@/views/lensTypes/ManageLensType')),
        authority: [],
    },
    {
        key: 'lensType',
        path: '/manage-lens-type/:id',
        component: lazy(() => import('@/views/lensTypes/ManageLensType')),
        authority: [],
    },
    {
        key: 'lensIndex',
        path: '/lens-index',
        component: lazy(() => import('@/views/lensIndex/LensIndex')),
        authority: [],
    },
    {
        key: 'lensIndex',
        path: '/manage-lens-index',
        component: lazy(() => import('@/views/lensIndex/ManageLensIndex')),
        authority: [],
    },
    {
        key: 'lensIndex',
        path: '/manage-lens-index/:id',
        component: lazy(() => import('@/views/lensIndex/ManageLensIndex')),
        authority: [],
    },
    {
        key: 'coating',
        path: '/coating',
        component: lazy(() => import('@/views/coatings/Coating')),
        authority: [],
    },
    {
        key: 'coating',
        path: '/manage-coating',
        component: lazy(() => import('@/views/coatings/ManageCoating')),
        authority: [],
    },
    {
        key: 'coating',
        path: '/manage-coating/:id',
        component: lazy(() => import('@/views/coatings/ManageCoating')),
        authority: [],
    },
    {
        key: 'imageMap',
        path: '/image-map/:id',
        component: lazy(() => import('@/views/homeArrange/ImageMap')),
        authority: [],
    },
    {
        key: 'newsLetter',
        path: '/news-letter',
        component: lazy(() => import('@/views/newsLetter/NewsLetter')),
        authority: [],
    },
    {
        key: 'coupons',
        path: '/coupons',
        component: lazy(() => import('@/views/coupons/Coupons')),
        authority: [],
    },
    {
        key: 'coupons',
        path: '/manage-coupons',
        component: lazy(() => import('@/views/coupons/ManageCoupon')),
        authority: [],
    },
    {
        key: 'coupons',
        path: '/manage-coupons/:id',
        component: lazy(() => import('@/views/coupons/ManageCoupon')),
        authority: [],
    },
    {
        key: 'label',
        path: '/label',
        component: lazy(() => import('@/views/labels/Label')),
        authority: [],
    },
    {
        key: 'label',
        path: '/manage-label',
        component: lazy(() => import('@/views/labels/ManageLabel')),
        authority: [],
    },
    {
        key: 'label',
        path: '/manage-label/:id',
        component: lazy(() => import('@/views/labels/ManageLabel')),
        authority: [],
    },
    {
        key : 'contactLens',
        path : '/contact-lens-power',
        component: lazy(() => import('@/views/contactLens/ContactPower')),
        authority : [],
    },
    {
        key : 'contactLens',
        path : '/manage-contact-lens-power/:type',
        component: lazy(() => import('@/views/contactLens/ManageContactPower')),
        authority : [],
    },
    {
        key : 'contactLens',
        path : '/manage-contact-lens-power/:type/:id',
        component: lazy(() => import('@/views/contactLens/ManageContactPower')),
        authority : [],
    },
    {
        key : 'footer',
        path : '/footer',
        component: lazy(() => import('@/views/headerMenu/Footer')),
        authority : [],
    },
    {
        key : 'roles',
        path : '/roles',
        component: lazy(() => import('@/views/adminUser/Roles')),
        authority : [],
    },
    {
        key : 'roles',
        path : '/manage-roles',
        component: lazy(() => import('@/views/adminUser/ManageRoles')),
        authority : [],
    },
    {
        key : 'roles',
        path : '/manage-roles/:id',
        component: lazy(() => import('@/views/adminUser/ManageRoles')),
        authority : [],
    },
    {
        key: 'reports',
        path: '/sales-report',
        component: lazy(() => import('@/views/reports/SalesReport')),
        authority: [],
    },
    {
        key: 'reports',
        path: '/revenue-report',
        component: lazy(() => import('@/views/reports/RevenueReport')),
        authority: [],
    },
    {
        key: 'reports',
        path: '/top-selling-report',
        component: lazy(() => import('@/views/reports/TopSellingReport')),
        authority: [],
    },
    {
        key: 'reports',
        path: '/customer-report',
        component: lazy(() => import('@/views/reports/CustomerReport')),
        authority: [],
    },
    {
        key: 'reports',
        path: '/guest-report',
        component: lazy(() => import('@/views/reports/GuestReport')),
        authority: [],
    },
    {
        key : 'non-list',
        path : '/non-list',
        component: lazy(() => import('@/views/nonList/NonList')),
        authority : [],
    },
    {
        key : 'non-list',
        path : '/manage-non-list',
        component: lazy(() => import('@/views/nonList/ManageNonList')),
        authority : [],
    },
    {
        key : 'non-list',
        path : '/manage-non-list/:id',
        component: lazy(() => import('@/views/nonList/ManageNonList')),
        authority : [],
    },
    {
        key : 'non-list',
        path : '/product-enquiries',
        component: lazy(() => import('@/views/nonList/Enquiries')),
        authority : [],
    },
    {
        key: 'lensEnquiry',
        path: '/lens-enquiries',
        component: lazy(() => import('@/views/pages/LensEnquiries')),
        authority: [],
    },
    {
        key: 'lensEnquiry',
        path: '/lens-enquiries/:id',
        component: lazy(() => import('@/views/pages/LensEnquiryDetails')),
        authority: [],
    },
    {
        key : 'reviews',
        path : '/reviews',
        component: lazy(() => import('@/views/catalog/products/Reviews')),
        authority : [],
    },
    {
        key : 'metaTags',
        path : '/meta-tags',
        component: lazy(() => import('@/views/pages/MetaTags')),
        authority : [],
    },
    {
        key : 'contactLens',
        path : '/contact-lenses',
        component: lazy(() => import('@/views/contactLens/contactLens')),
        authority : [],
    },
    {
        key : 'contactLens',
        path : '/manage-contact-lenses',
        component: lazy(() => import('@/views/contactLens/ManageContactLens')),
        authority : [],
    },
    {
        key : 'contactLens',
        path : '/manage-contact-lenses/:id',
        component: lazy(() => import('@/views/contactLens/ManageContactLens')),
        authority : [],
    },
    {
        key : 'frontMaterial',
        path : '/front-material',
        component: lazy(() => import('@/views/frontMaterial/FrontMaterialList')),
        authority : [],
    },
    {
        key : 'frontMaterial',
        path : '/manage-front-material',
        component: lazy(() => import('@/views/frontMaterial/ManageFrontMaterial')),
        authority : [],
    },
    {
        key : 'frontMaterial',
        path : '/manage-front-material/:id',
        component: lazy(() => import('@/views/frontMaterial/ManageFrontMaterial')),
        authority : [],
    },
    {
        key: 'type',
        path: '/types',
        component: lazy(() => import('@/views/types/TypeList')),
        authority: [],
    },
    {
        key: 'type',
        path: '/manage-types',
        component: lazy(() => import('@/views/types/ManageType')),
        authority: [],
    },
    {
        key: 'type',
        path: '/manage-types/:id',
        component: lazy(() => import('@/views/types/ManageType')),
        authority: [],
    },
    {
        key:'lensMaterial',
        path: '/lens-material',
        component: lazy(() => import('@/views/lensMaterial/LensMaterialList')),
        authority: [],
    },
    {
        key:'lensMaterial',
        path: '/manage-lens-material',
        component: lazy(() => import('@/views/lensMaterial/MangeLensMaterial')),
        authority: [],
    },
    {
        key:'lensMaterial',
        path: '/manage-lens-material/:id',
        component: lazy(() => import('@/views/lensMaterial/MangeLensMaterial')),
        authority: [],
    }

]