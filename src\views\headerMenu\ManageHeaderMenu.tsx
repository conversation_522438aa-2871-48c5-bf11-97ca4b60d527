/* eslint-disable */
import {
    <PERSON><PERSON>,
    FormContainer,
    FormItem,
    Input,
    Select,
    toast,
} from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { AiOutlineSave, AiOutlinePlus } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { pages } from './pageoptions'
import Breadcrumb from '../modals/BreadCrumb'

const imageurl = import.meta.env.VITE_ASSET_URL

export default function ManageHeaderMenu() {
    const [selectedMenuType, setSelectedMenuType] = useState('')
    const [isSubMenu, setIsSubMenu] = useState()
    const [categoryOptions, setCategoryOptions] = useState([])
    const [productOptions, setProductOptions] = useState([])
    const [brandOptions, setBrandOptions] = useState([])
    const [pageOptions, setPageOptions] = useState<any>(pages)
    const [collectionOptions, setCollectionOptions] = useState([])
    const [typeOptions, setTypeOptions] = useState([])
    const [subMenuPreview, setSubMenuPreview] =  useState<string[]>([])

    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Header Menu', url: '/header-menu' },
        {
            title: params?.id ? 'Update Header Menu' : 'Create Header Menu',
            url: '',
        },
    ]

    const types: any = [
        { value: 'category', label: 'category' },
        { value: 'product', label: 'product' },
        { value: 'brand', label: 'brand' },
        { value: 'collections', label: 'collections' },
        { value: 'page', label: 'page' },
    ]

    const options: any = [
        { value: true, label: 'true' },
        { value: false, label: 'false' },
    ]

    function getCategories() {
        api.get(endpoints.parentCategories)
            .then((res) => {
                if (res?.status == 200) {
                    const categories = res?.data?.result || []
                    const newCategoryOptions = categories.map(
                        (category: any) => ({
                            value: category?._id,
                            label: category?.name.en,
                            slug: category?.slug,
                        })
                    )
                    setCategoryOptions(newCategoryOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    function getBrands() {
        api.get(endpoints.brands)
            .then((res) => {
                if (res?.status == 200) {
                    const brands = res?.data?.result || []
                    const newBrandOptions = brands.map((brand: any) => ({
                        value: brand._id,
                        label: brand.name.en,
                        slug: brand.slug,
                    }))
                    setBrandOptions(newBrandOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    function getProducts() {
        api.post(endpoints.products)
            .then((res) => {
                if (res?.status == 200) {
                    const products = res?.data?.result?.products || []
                    const newProductOptions = products.map((product: any) => ({
                        value: product._id,
                        label: product.name.en,
                        slug: product.slug,
                    }))
                    setProductOptions(newProductOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    function getCollections() {
        api.get(endpoints.collections)
            .then((res) => {
                if (res?.status == 200) {
                    const collections = res?.data?.result || []
                    const newCollectionOptions = collections.map(
                        (collection: any) => ({
                            value: collection._id,
                            label: collection.title.en,
                            slug: collection.slug,
                        })
                    )
                    setCollectionOptions(newCollectionOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const handleSubMenuUpload = (e:any, index: number) => {
        const image = e.target.files[0]
        if (image) {
            const formData = new FormData()
            formData.append('video', image)

            api.post(endpoints.videoUpload, formData)
                .then((res) => {
                    const imageUrl = res?.data?.videoUrl
                    setValue(`submenus.${index}.imageUrl`, imageUrl)
                    setSubMenuPreview((prevPreviews) => {
                        const newPreviews = [...prevPreviews]
                        newPreviews[index] = imageUrl
                        return newPreviews
                    })
                })
                .catch((err) => {
                    console.log('err', err)
                })
        }
    }

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const {
        fields: submenuFields,
        append: submenuAppend,
        remove: submenuRemove,
    } = useFieldArray({
        control,
        name: 'submenus',
    })

    useEffect(() => {
        getCategories()
        getBrands()
        getProducts()
        getCollections()

        if (params.id) {
            api.get(endpoints.menuDetail + params.id)
                .then((res) => {
                    if (res.status == 200) {
                        setValue('titleEn', res.data.result.title.en)
                        setValue('titleAr', res.data.result.title.ar)
                        setValue('link', res.data?.result?.link)
                        setValue('isSubmenu', {
                            value: res.data.result.isSubmenu,
                            label: res.data.result.isSubmenu.toString(),
                        })
                        setIsSubMenu(res.data.result.isSubmenu)
                        setValue('redirection', res.data.result.redirection)

                        setValue('menuType', {
                            value: res.data.result.menuType,
                            label: res.data.result.menuType,
                        })
                        setSelectedMenuType(res.data.result.menuType)

                        if (res?.data?.result?.submenus?.length > 0) {
                            setValue(
                                'submenus',
                                res.data.result.submenus.map(
                                    (submenu: any) => ({
                                        titleEn: submenu.title.en,
                                        titleAr: submenu.title.ar,
                                        type: {
                                            value: submenu.menuType,
                                            label: submenu.menuType,
                                        },
                                        redirection: submenu?.redirection,
                                        imageUrl: submenu?.image,
                                    })
                                )
                            )

                           setSubMenuPreview(res?.data?.result?.submenus?.map((submenu: any) => submenu?.image)) 
                        }
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }, [params.id])

    const onSubmit = (data: any) => {
        console.log('data', data)
        const formData = new FormData()
        const values: any = {
            title: {
                en: data?.titleEn,
                ar: data?.titleAr,
            },
            link: data?.link,
            isSubmenu: data?.isSubmenu?.value,
            menuType: data?.menuType?.value,
            redirection: data.redirection,
            submenus: data.submenus.map((submenu: any, index: number) => ({
                title: {
                    en: submenu?.titleEn,
                    ar: submenu?.titleAr,
                },
                menuType: submenu?.type?.value,
                redirection: submenu?.redirection,
                image: submenu?.imageUrl,
            })),
        }

        formData.append('data', JSON.stringify(values))

        if (params.id) {
            api.put(endpoints.updateMenu + params.id, formData).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data?.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    navigate('/header-menu')
                }
            })
        } else {
            api.post(endpoints.createMenu, formData).then((res) => {
                console.log(res)
                if (res?.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data?.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    navigate('/header-menu')
                }
            })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params.id ? 'Edit' : 'Add '} Header Menu</h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Title (English)">
                    <Controller
                        name="titleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.titleEn
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.titleEn && (
                        <small className="text-red-600 py-3">
                            {errors.titleEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Title (Arabic)">
                    <Controller
                        name="titleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                // className={`${
                                //     errors.titleAr
                                //         ? ' input input-md h-11 input-invalid'
                                //         : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                // }`}
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.titleAr && (
                        <small className="text-red-600 py-3">
                            {errors.titleAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Redirection Link">
                        <Controller
                            name="link"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Link is required' }}
                            render={({ field }) => (
                                <Input
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.link && (
                            <small className="text-red-600 py-3">
                                {errors.link.message as string}
                            </small>
                        )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Have Sub Menus ? ">
                    <Controller
                        name="isSubmenu"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Required Field' }}
                        render={({ field }) => (
                            <Select
                                defaultValue=""
                                isSearchable={false}
                                options={options}
                                {...field}
                                onChange={(value: any) => {
                                    field.onChange(value)
                                    setIsSubMenu(value?.value)
                                    if (value?.value == true) {
                                        submenuAppend({})
                                    } else {
                                        submenuRemove(0)
                                    }
                                }}
                            />
                        )}
                    />
                    {errors.isSubmenu && (
                        <small className="text-red-600 py-3">
                            {errors.isSubmenu.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            {isSubMenu === true && (
                <ul className="mt-4">
                    {submenuFields.map((field, index) => {
                        return (
                            <li key={field.id}>
                                <FormContainer className="grid grid-cols-2 gap-4">
                                    <FormItem label="Title (English)">
                                        <Controller
                                            name={`submenus.${index}.titleEn`}
                                            control={control}
                                            defaultValue={''}
                                            rules={{
                                                required: 'Title is required',
                                            }}
                                            render={({ field }) => (
                                                <Input type="text" {...field} />
                                            )}
                                        />
                                        {errors?.submenus &&
                                            Array.isArray(errors.submenus) &&
                                            errors.submenus[index]?.titleEn && (
                                                <small className="text-red-600 py-3">
                                                    {
                                                        errors.submenus[index]
                                                            .titleEn
                                                            .message as string
                                                    }
                                                </small>
                                            )}
                                    </FormItem>
                                    <FormItem label="Title (Arabic)">
                                        <Controller
                                            defaultValue={''}
                                            // rules={{
                                            //     required: 'Title is required',
                                            // }}
                                            render={({ field }) => (
                                                <Input
                                                    dir="rtl"
                                                    type="text"
                                                    {...field}
                                                />
                                            )}
                                            name={`submenus.${index}.titleAr`}
                                            control={control}
                                        />
                                        {/* {errors?.submenus &&
                                            Array.isArray(errors.submenus) &&
                                            errors.submenus[index]?.titleAr && (
                                                <small className="text-red-600 py-3">
                                                    {
                                                        errors.submenus[index]
                                                            .titleAr
                                                            .message as string
                                                    }
                                                </small>
                                            )} */}
                                    </FormItem>
                                </FormContainer>

                                <FormContainer className="grid grid-cols-2 gap-4">
                                    {/* <FormItem label="Type">
                                        <Controller
                                            name={`submenus.${index}.type`}
                                            control={control}
                                            defaultValue=""
                                            rules={{
                                                required: 'Type is required',
                                            }}
                                            render={({ field }) => (
                                                <Select
                                                    defaultValue=""
                                                    placeholder="Select Type"
                                                    isSearchable={false}
                                                    options={types}
                                                    {...field}
                                                    onChange={(
                                                        selectedOption
                                                    ) => {
                                                        field.onChange(
                                                            selectedOption
                                                        )
                                                        setSelectedMenuType(
                                                            selectedOption.label
                                                        )
                                                        switch (
                                                            selectedOption.value
                                                        ) {
                                                            case 'category':
                                                                setTypeOptions(
                                                                    categoryOptions
                                                                )
                                                                break
                                                            case 'product':
                                                                setTypeOptions(
                                                                    productOptions
                                                                )
                                                                break
                                                            case 'brand':
                                                                setTypeOptions(
                                                                    brandOptions
                                                                )
                                                                break
                                                            case 'collections':
                                                                setTypeOptions(
                                                                    collectionOptions
                                                                )
                                                                break
                                                            case 'page':
                                                                setTypeOptions(
                                                                    pageOptions
                                                                )
                                                                break
                                                        }
                                                    }}
                                                />
                                            )}
                                        />
                                        {errors?.submenus &&
                                            Array.isArray(errors.submenus) &&
                                            errors.submenus[index]?.type && (
                                                <small className="text-red-600 py-3">
                                                    {
                                                        errors.submenus[index]
                                                            .type
                                                            .message as string
                                                    }
                                                </small>
                                            )}
                                    </FormItem> */}

                                    {/* {selectedMenuType && (
                                        <FormItem
                                            label={`Select ${selectedMenuType}`}
                                        >
                                            <Controller
                                                name={`submenus.${index}.value`}
                                                control={control}
                                                defaultValue=""
                                                rules={{
                                                    required: 'Required Field',
                                                }}
                                                render={({ field }) => (
                                                    <Select
                                                        defaultValue=""
                                                        placeholder={`Select ${selectedMenuType}`}
                                                        isSearchable={false}
                                                        options={typeOptions}
                                                        {...field}
                                                        onChange={(
                                                            selectedOption
                                                        ) => {
                                                            field.onChange(
                                                                selectedOption
                                                            )
                                                            setValue(
                                                                'submenus.' +
                                                                    index +
                                                                    '.redirection',
                                                                selectedOption.slug
                                                            )
                                                        }}
                                                    />
                                                )}
                                            />
                                            {errors?.submenus &&
                                                Array.isArray(
                                                    errors.submenus
                                                ) &&
                                                errors.submenus[index]
                                                    ?.value && (
                                                    <small className="text-red-600 py-3">
                                                        {
                                                            errors.submenus[
                                                                index
                                                            ].value
                                                                .message as string
                                                        }
                                                    </small>
                                                )}
                                        </FormItem>
                                    )} */}
                                </FormContainer>

                                <FormContainer className="grid grid-cols-2 gap-4">
                                    <FormItem label="Redirection">
                                        <Controller
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <Input type="text" {...field} />
                                            )}
                                            name={`submenus.${index}.redirection`}
                                            control={control}
                                        />
                                    </FormItem>

                                    <FormItem label="Image (Preferred Size: 350x300 px)">
                                        <Controller
                                            name={`submenus.${index}.image`}
                                            control={control}
                                            defaultValue={null}
                                            render={({ field }) => (
                                                <div>
                                                    <Input
                                                        type="file"
                                                        {...field}
                                                        onChange={(e) => {
                                                            field.onChange(e)
                                                            handleSubMenuUpload(
                                                                e,
                                                                index
                                                            )
                                                        }}
                                                    />

                                                    {subMenuPreview[index] && (
                                                        <img
                                                            className="mt-2 w-20 mx-auto object-cover object-top"
                                                            src={
                                                                imageurl +
                                                                subMenuPreview[
                                                                    index
                                                                ]
                                                            }
                                                            alt={`Image Preview ${index}`}
                                                        />
                                                    )}
                                                </div>
                                            )}
                                        />
                                    </FormItem>
                                </FormContainer>

                                <Button
                                    className="mb-2"
                                    type="button"
                                    variant='solid'
                                    color='red'
                                    onClick={() => submenuRemove(index)}
                                >
                                    Delete Submenu
                                </Button>
                            </li>
                        )
                    })}
                    <Button
                        variant="solid"
                        color="green"
                        className="mt-4"
                        icon={<AiOutlinePlus />}
                        type="button"
                        onClick={() => submenuAppend({})}
                    >
                        Add More
                    </Button>
                </ul>
            )}

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
