/* eslint-disable */
import { Button, FormItem, Select, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

const baseUrl = import.meta.env.VITE_ASSET_URL

export default function LensEnquiryDetails() {
    const [brands, setBrands] = useState<any[]>([])
    const [enquiry, setEnquiry] = useState<any>(null)
    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Lens Enquiries', url: '/lens-enquiries' },
        { title: params?.id ?? "", url: '' },
    ];

    useEffect(() => {
        if (params.id)
            api.get(endpoints.lensEnquiries + "/" + params.id).then((res) => {
                if (res?.status == 200) {
                    console.log(res.data.result[0])
                    const data = res.data.result[0]
                    setEnquiry(data)
                    setValue('name', data?.userDetails?.name)
                    setValue('mobile', data?.userDetails?.phone)
                    setValue('address', data?.userDetails?.message)
                    setValue('leftSph', data?.prescription?.leftSph?.name)
                    setValue('rightSph', data?.prescription?.rightSph?.name)
                    setValue('leftAxis', data?.prescription?.leftAxis?.name)
                    setValue('rightAxis', data?.prescription?.rightAxis?.name)
                    setValue('leftCyl', data?.prescription?.leftCyl?.name)
                    setValue('rightCyl', data?.prescription?.rightCyl?.name)
                    setValue('leftAdd', data?.prescription?.leftAdd?.name)
                    setValue('rightAdd', data?.prescription?.rightAdd?.name)
                    setValue('pd', data?.prescription?.pd)
                    setValue('vision', data?.vision)
                    setValue('brand', data?.brand?.name?.en)
                    setValue('product', data?.product?.name?.en)

                }
            }).catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }, [])


    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const print = () => {
        window.print()
    }

    return (
        <form >
            <h3 className="mb-2">Enquiry Details </h3>
            <Breadcrumb  items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label='Name'>
                    <Controller
                        control={control}
                        name="name"
                        defaultValue={''}
                        disabled
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                </FormItem>
                <FormItem label='Mobile'>
                    <Controller
                        control={control}
                        name="mobile"
                        defaultValue={''}
                        disabled
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Address'>
                    <Controller
                        control={control}
                        name="address"
                        defaultValue={''}
                        disabled
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                textArea
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                </FormItem>
            </div>
            <FormItem label='Product'>
                <Controller
                    control={control}
                    name="product"
                    defaultValue={''}
                    disabled
                    rules={{ required: 'Field Required' }}
                    render={({ field }) => (
                        <Input
                            type="text"
                            {...field}
                        />
                    )}
                />
                {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
            </FormItem>
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label='Vision'>
                    <Controller
                        control={control}
                        name="vision"
                        defaultValue={''}
                        disabled
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                </FormItem>
                <FormItem label='Brand'>
                    <Controller
                        control={control}
                        name="brand"
                        defaultValue={''}
                        disabled
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                </FormItem>
            </div>
            {enquiry?.prescription && (
                <>
                    <h4 className="mb-2">Prescription</h4>
                    <div className='grid grid-cols-2 gap-4'>
                        {enquiry?.prescription?.leftSph && <FormItem label='Left Sph'>
                            <Controller
                                control={control}
                                name='leftSph'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                        {enquiry?.prescription?.rightSph && <FormItem label='Right Sph'>
                            <Controller
                                control={control}
                                name='rightSph'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                    </div>
                    <div className='grid grid-cols-2 gap-4'>
                        {enquiry?.prescription?.leftAxis && <FormItem label='Left Axis'>
                            <Controller
                                control={control}
                                name='leftAxis'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                        {enquiry?.prescription?.rightAxis && <FormItem label='Right Axis'>
                            <Controller
                                control={control}
                                name='rightAxis'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                    </div>
                    <div className='grid grid-cols-2 gap-4'>
                        {enquiry?.prescription?.leftCyl && <FormItem label='Left Cyl'>
                            <Controller
                                control={control}
                                name='leftCyl'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                        {enquiry?.prescription?.rightCyl && <FormItem label='Right Cyl'>
                            <Controller
                                control={control}
                                name='rightCyl'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                    </div>
                    <div className='grid grid-cols-2 gap-4'>
                        {enquiry?.prescription?.leftAdd && <FormItem label='Left Add'>
                            <Controller
                                control={control}
                                name='leftAdd'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                        {enquiry?.prescription?.rightAdd && <FormItem label='Right Add'>
                            <Controller
                                control={control}
                                name='rightAdd'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                    </div>
                    <div className='grid grid-cols-2 gap-4'>
                        {enquiry?.prescription?.pd && <FormItem label='PD'>
                            <Controller
                                control={control}
                                name='pd'
                                disabled
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                        </FormItem>}
                    </div>
                </>
            )}
            {enquiry?.prescriptionFile && (
                <>
                    <h4>Prescription File</h4>
                    <img className='max-w-full max-h-[500px] object-contain' src={baseUrl + enquiry.prescriptionFile} />
                </>
            )}
            <Button type="button" className="float-right mt-4 print:hidden"
                variant="solid"
                onClick={print}>
                Download
            </Button>
        </form>
    )
}