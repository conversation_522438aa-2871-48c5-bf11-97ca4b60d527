/*eslint-disable */
import { FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import Upload from '@/components/ui/Upload'
import { useEffect, useState } from 'react'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Breadcrumb from '@/views/modals/BreadCrumb'
import { Controller, useForm } from 'react-hook-form'
import { Select } from '@/components/ui'
import { AiOutlineSave } from 'react-icons/ai'

const imageUrl = import.meta.env.VITE_ASSET_URL

const breadcrumbItems = [
    { title: 'Brands', url: '/catalog/brands' },
    { title: 'Manage Brands', url: '' },
]

const ManageBrand = () => {
    const navigate = useNavigate()
    const params = useParams()
    const [stores, setStores] = useState([] as any)
    const [brandId, setBrandId] = useState("")

    const [bannerFiles, setbannerFiles] = useState([] as any)
    const [bannerFilesAr, setbannerFilesAr] = useState([] as any)

    const [brandFile, setBrandFile] = useState([] as any)

    const [ogImageFile, setOgImageFile] = useState([] as any)
    const [ogImageError, setOgImageError] = useState<any>(null)

    const [posterFile, setPosterFile] = useState([] as any)
    const [posterFileAr, setPosterFileAr] = useState([] as any)

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const {
        handleSubmit,
        setValue,
        register,
        control,
        formState: { errors },
    } = useForm<any>()

    const getStores = () => {
        api.get(endpoints.stores)
            .then((res) => {
                if (res?.status == 200) {
                    const stores = res.data.result || []
                    const newStoreOptions = stores.map((store: any) => ({
                        value: store._id,
                        label: store.name.en,
                    }))
                    setStores(newStoreOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const handleBannerUpload = (files: any) => {
        setbannerFiles(files)
    }
    const handleBannerUploadAr = (files: any) => {
        setbannerFilesAr(files)
    }

    const handleImageUpload = (files: any) => {
        setBrandFile(files)
    }

    const handlePosterUpload = (files: any) => {
        setPosterFile(files)
    }
    const handlePosterUploadAr = (files: any) => {
        setPosterFileAr(files)
    }

    const handleOgImageUpload = (files: any) => {
        setOgImageFile(files)
    }

    useEffect(() => {
        getStores()
        if (params.id)
            api.get(endpoints.brandDetail + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        const brandData = res.data.result
                        console.log(brandData, "brand data")
                        setBrandId(brandData?.brandId)
                        setValue('nameEn', brandData.name?.en)
                        setValue('nameAr', brandData.name?.ar)
                        setValue('overviewEn', brandData?.overview?.en)
                        setValue('overviewAr', brandData?.overview?.ar)
                        setValue(
                            'stores',
                            brandData?.store?.map((store: any) => ({
                                value: store?._id,
                                label: store.name?.en,
                            }))
                        )
                        setValue('isActive', {
                            value: brandData.isActive,
                            label: brandData.isActive ? 'True' : 'False',
                        })
                        setValue('isCashbackEnabled', {
                            value: brandData?.isCashbackEnabled,
                            label: brandData?.isCashbackEnabled ? 'True' : 'False',
                        })
                        setValue('cashbackPercentage', brandData?.cashbackPercentage)
                        setValue('position', brandData?.position)
                        setValue('metaTitleEn', brandData?.seoDetails?.title?.en)
                        setValue('metaTitleAr', brandData?.seoDetails?.title?.ar)
                        setValue('metaDescriptionEn', brandData?.seoDetails?.description?.en)
                        setValue('metaDescriptionAr', brandData?.seoDetails?.description?.ar)
                        setValue('metaKeywordsEn', brandData?.seoDetails?.keywords?.en)
                        setValue('metaKeywordsAr', brandData?.seoDetails?.keywords?.ar)
                        setValue('metaCanonicalUrl', brandData?.seoDetails?.canonical?.en)
                        setValue('metaCanonicalUrlAr', brandData?.seoDetails?.canonical?.ar)

                        setBrandFile([imageUrl + brandData?.image])
                        setOgImageFile([imageUrl + brandData?.seoDetails?.ogImage])
                        setbannerFiles([imageUrl + brandData?.banner?.en])
                        setbannerFilesAr([imageUrl + brandData?.banner?.ar])
                        setPosterFile([imageUrl + brandData?.poster?.en])
                        setPosterFileAr([imageUrl + brandData?.poster?.ar])
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
    }, [params])

    const onSubmit = handleSubmit(async (data: any) => {
        if (brandFile.length == 0) {
            toast.push(
                <Notification
                    type="warning"
                    title="Please Upload Brand Image"
                />
            )
            return
        }

        if (bannerFiles.length == 0) {
            toast.push(
                <Notification
                    type="warning"
                    title="Please Upload Banner Image"
                />
            )
            return
        }

        if (posterFile.length == 0) {
            toast.push(
                <Notification
                    type="warning"
                    title="Please Upload Poster Image"
                />
            )
            return
        }

        if (ogImageFile.length == 0) {
            setOgImageError('OG Image is required')
            return
        }

        const formData = new FormData()
        const name = {
            en: data.nameEn?.trim(),
            ar: data.nameAr.trim(),
        }

        formData.append('name[en]', name.en)
        formData.append('name[ar]', name.ar)
        formData.append('overview[en]', data.overviewEn)
        formData.append('overview[ar]', data.overviewAr)
        formData.append('seoDetails[title][en]', data.metaTitleEn)
        formData.append('seoDetails[title][ar]', data.metaTitleAr)
        formData.append('seoDetails[description][en]', data.metaDescriptionEn)
        formData.append('seoDetails[description][ar]', data.metaDescriptionAr)
        formData.append('seoDetails[keywords][en]', data.metaKeywordsEn)
        formData.append('seoDetails[keywords][ar]', data.metaKeywordsAr)
        formData.append('seoDetails[canonical][en]', data.metaCanonicalUrl)
        formData.append('seoDetails[canonical][ar]', data.metaCanonicalUrlAr)
        
        if (data?.stores?.length > 0) {
            for (let i = 0; i < data?.stores?.length; i++) {
                formData.append('store', data.stores[i].value)
            }
        }
        if (data?.isActive?.value) formData.append('isActive', data?.isActive?.value);
        if (data?.isCashbackEnabled?.value) formData.append('isCashbackEnabled', data?.isCashbackEnabled?.value);
        formData.append('cashbackPercentage', data?.cashbackPercentage)
        formData.append('image', brandFile[0])
        formData.append('position', data?.position)

        formData.append('banner', bannerFiles[0])
        formData.append('bannerAr', bannerFilesAr[0])
        formData.append('poster', posterFile[0])
        formData.append('posterAr', posterFileAr[0])
        formData.append('ogImage', ogImageFile[0])
        formData.append('brandId', brandId)
        if (params.id) {
            formData.append('refid', params.id)

            api.post(endpoints.updateBrand, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/catalog/brands')
                    }
                })
                .catch((error) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={error.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            api.post(endpoints.createBrand, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/catalog/brands')
                    }
                })
                .catch((error) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={error.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        }
    })

    return (
        <div className="p-4">
            <form onSubmit={onSubmit}>
                <h3>{params.id ? 'Edit' : 'Add'} Brand</h3>
                <Breadcrumb items={breadcrumbItems} />
                <h4>ID: {brandId}</h4>
                <h5 className="mt-4">Name</h5>
                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="English">
                        <Controller
                            name="nameEn"
                            rules={{ required: 'Field Required' }}
                            control={control}
                            defaultValue=""
                            render={({ field }) => <Input {...field} />}
                        />
                        {errors.nameEn && (
                            <small className="text-red-600">
                                This field is required
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Arabic">
                        <Controller
                            name="nameAr"
                            control={control}
                            defaultValue=""
                            render={({ field }) => <Input dir='rtl' {...field} />}
                        />
                    </FormItem>
                </div>

                <h5>Overview</h5>
                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="Description English">
                        <Controller
                            name="overviewEn"
                            control={control}
                            defaultValue=""
                            rules={{ required: true }}
                            render={({ field }) => (
                                <Input textArea {...field} />
                            )}
                        />
                        {errors.overviewEn && (
                            <small className="text-red-600">
                                This field is required
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Description Arabic">
                        <Controller
                            name="overviewAr"
                            control={control}
                            defaultValue=""
                            rules={{ required: true }}
                            render={({ field }) => (
                                <Input dir='rtl' textArea {...field} />
                            )}
                        />
                        {errors.overviewAr && (
                            <small className="text-red-600">
                                This field is required
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Stores">
                        <Controller
                            name="stores"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field Required' }}
                            render={({ field }) => (
                                <Select {...field} options={stores} isMulti />
                            )}
                        />
                        {errors.stores && (
                            <small className="text-red-600">
                                This field is required
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Position">
                        <Controller
                            name="position"
                            rules={{ required: 'Field Required' }}
                            control={control}
                            defaultValue=""
                            render={({ field }) => <Input type='number' {...field} />}
                        />
                        {errors.position && (
                            <small className="text-red-600">
                                This field is required
                            </small>
                        )}
                    </FormItem>
                </div>

                {params.id && (
                    <div className="grid grid-cols-2 gap-4 mt-2">
                        <FormItem label="Is Active?">
                            <Controller
                                name="isActive"
                                control={control}
                                defaultValue=""
                                render={({ field }) => (
                                    <Select {...field} options={options} />
                                )}
                            />
                        </FormItem>
                    </div>
                )}
                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="Cashback">
                        <Controller
                            name="isCashbackEnabled"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select {...field} options={options} />
                            )}
                        />
                    </FormItem>
                    <FormItem label="Cashback Percentage">
                        <Controller
                            name="cashbackPercentage"
                            control={control}
                            defaultValue="0"
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input min={0} type="number" {...field} />
                            )}
                        />
                        {errors.cashbackPercentage && (
                            <small className="text-red-600 py-3">
                                {errors.cashbackPercentage.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>
                <div className="mt-2">
                    <FormItem label="Brand Image">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={brandFile}
                            onChange={handleImageUpload}
                            ratio={[360, 180]}
                        />
                    </FormItem>
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Banner Image English">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={bannerFiles}
                            onChange={handleBannerUpload}
                            ratio={[1332, 240]}
                        />
                    </FormItem>
                    <FormItem label="Banner Image Arabic">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={bannerFilesAr}
                            onChange={handleBannerUploadAr}
                            ratio={[1332, 240]}
                        />
                    </FormItem>
                </div>
                <div className='grid grid-cols-2 gap-4'>
                    <FormItem label="Brand Poster English">

                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={posterFile}
                            onChange={handlePosterUpload}
                            ratio={[360, 180]}
                        />
                    </FormItem>
                    <FormItem label="Brand Poster Arabic">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={posterFileAr}
                            onChange={handlePosterUploadAr}
                            ratio={[360, 180]}
                        />
                    </FormItem>
                </div>

                <h5>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
               <FormItem label="Meta OG Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={ogImageFile}
                        onChange={handleOgImageUpload}
                        ratio={[1126, 1200]}
                    />
                    {ogImageError && <small className="text-red-600">{ogImageError}</small>}
                </FormItem>
                
            </div>

                <Button
                    className="float-right mt-4"
                    variant="solid"
                    type="submit"
                    icon={<AiOutlineSave />}
                >
                    Save
                </Button>
            </form>
        </div>
    )
}

export default ManageBrand
